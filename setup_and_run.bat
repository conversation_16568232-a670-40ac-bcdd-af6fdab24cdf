@echo off
setlocal enabledelayedexpansion
chcp 65001 >nul
echo ================================================
echo Word表格批量复制工具 - 自动安装运行脚本
echo ================================================

REM 1. 检测Python环境
echo [1/4] 正在检测Python环境...
set PYTHON_CMD=

REM 尝试python命令
python --version >nul 2>&1
if !errorlevel! == 0 (
    set PYTHON_CMD=python
    goto :found_python
)

REM 尝试py命令
py --version >nul 2>&1
if !errorlevel! == 0 (
    set PYTHON_CMD=py
    goto :found_python
)

echo ✗ 错误: 未找到Python环境
echo   请先安装Python 3.7或更高版本
echo   下载地址: https://www.python.org/downloads/
pause
exit /b 1

:found_python
echo ✓ 找到Python: !PYTHON_CMD!

REM 2. 安装依赖库
echo [2/4] 正在安装python-docx库...
!PYTHON_CMD! -m pip install python-docx --quiet --disable-pip-version-check

if !errorlevel! == 0 (
    echo ✓ python-docx库安装成功
) else (
    echo ⚠ 警告: python-docx库安装失败，尝试继续运行...
)

REM 3. 检查输入文件
echo [3/4] 正在检查输入文件...
if exist "internal\model\user\word.docx" (
    echo ✓ 找到输入文件: internal\model\user\word.docx
) else (
    echo ✗ 错误: 未找到输入文件 internal\model\user\word.docx
    echo   请确保文件存在且路径正确
    pause
    exit /b 1
)

REM 4. 运行处理脚本
echo [4/4] 正在处理Word文档...
echo.
!PYTHON_CMD! process_word_table.py

echo.
echo ================================================
if !errorlevel! == 0 (
    echo ✓ 处理完成！请检查生成的文件
) else (
    echo ✗ 处理过程中出现错误
)
echo ================================================
pause
