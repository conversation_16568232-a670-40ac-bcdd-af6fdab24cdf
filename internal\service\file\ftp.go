/*
******		FileName	:	ftp.go
******		Describe	:	此文件主要用于文件管理 ftp 服务实现
******		Date		:	2025-07-17
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:   文件服务相关的业务逻辑实现
 */

package server

import (
	"ayj_file_back/internal/dao"
	modelFile "ayj_file_back/internal/model/file"
	modelUser "ayj_file_back/internal/model/user"
	"ayj_file_back/internal/public/tools"
	"ayj_file_back/internal/public/uniqueId"
	"context"
	"crypto/sha256"
	"encoding/hex"
	"errors"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/goftp/server"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/os/gtime"
	"gorm.io/gorm"
)

// ServerFtpFile FTP文件服务器结构体
type ServerFtpFile struct {
	ftpServer *server.Server // FTP服务器实例
	mu        sync.RWMutex   // 读写锁，保证并发安全
}

// 全局变量定义
var (
	ftpInstance *ServerFtpFile // FTP服务器单例实例
	ftpOnce     sync.Once      // 确保单例模式的Once对象
)

// 1. FtpServer 获取FTP服务实例（单例模式）
func FtpServer() *ServerFtpFile {
	ftpOnce.Do(func() {
		ftpInstance = &ServerFtpFile{}
	})
	return ftpInstance
}

// CustomFtpDriver 自定义FTP驱动器结构体
type CustomFtpDriver struct {
	RootPath    string          // FTP根目录路径
	server.Perm                 // 权限控制器
	ctx         context.Context // 上下文对象
	userId      string          // 登录用户ID
	userPhone   string          // 登录用户手机号
	conn        *server.Conn    // FTP连接对象
	auth        *CustomAuth     // 认证器引用
}

// CustomFileInfo 自定义文件系统接口实现，实现server.FileInfo接口
type CustomFileInfo struct {
	name    string      // 文件名
	size    int64       // 文件大小
	mode    os.FileMode // 文件权限模式
	modTime time.Time   // 修改时间
	isDir   bool        // 是否为目录
}

// Name 获取文件名
func (f *CustomFileInfo) Name() string { return f.name }

// Size 获取文件大小
func (f *CustomFileInfo) Size() int64 { return f.size }

// Mode 获取文件权限模式
func (f *CustomFileInfo) Mode() os.FileMode { return f.mode }

// ModTime 获取修改时间
func (f *CustomFileInfo) ModTime() time.Time { return f.modTime }

// IsDir 判断是否为目录
func (f *CustomFileInfo) IsDir() bool { return f.isDir }

// Sys 获取系统相关信息
func (f *CustomFileInfo) Sys() interface{} {
	return nil
}

// Owner 获取文件所有者（实现server.FileInfo接口）
func (f *CustomFileInfo) Owner() string {
	return "root"
}

// Group 获取文件组（实现server.FileInfo接口）
func (f *CustomFileInfo) Group() string {
	return "root"
}

// 4. Init 初始化FTP连接 - 简化版本
func (d *CustomFtpDriver) Init(conn *server.Conn) {
	// 1. 保存连接对象
	d.conn = conn

	// 2. 简单记录连接初始化（暂时不获取具体IP）
	g.Log().Info(d.ctx, "FTP客户端连接初始化:", map[string]interface{}{
		"login user:": conn.LoginUser(), // 使用连接指针作为标识
		"status":      "等待用户认证",
		"ClientIp":    conn.ConnClient.RemoteAddr(),
	})
}

// 新增：设置用户信息的方法 - 简化版本
func (d *CustomFtpDriver) setUserInfo(userInfo *modelUser.UserInfo) {
	if userInfo != nil {
		d.userId = userInfo.UserId
		d.userPhone = userInfo.UserPhone

		// 打印用户认证成功信息
		g.Log().Info(d.ctx, "FTP用户认证成功:", map[string]interface{}{
			"connection_id": fmt.Sprintf("%p", d.conn), // 使用连接指针作为标识
			"user_id":       d.userId,
			"user_phone":    d.userPhone,
			"user_nick":     userInfo.UserNick,
		})
	}
}

// Stat 获取文件或目录信息（返回server.FileInfo接口）
func (d *CustomFtpDriver) Stat(path string) (server.FileInfo, error) {
	realPath := d.realPath(path)

	// 检查文件是否存在
	if !gfile.Exists(realPath) {
		return nil, fmt.Errorf("文件不存在: %s", path)
	}

	fileInfo, err := os.Stat(realPath)
	if err != nil {
		return nil, err
	}

	// 转换为自定义FileInfo
	customInfo := &CustomFileInfo{
		name:    fileInfo.Name(),
		size:    fileInfo.Size(),
		mode:    fileInfo.Mode(),
		modTime: fileInfo.ModTime(),
		isDir:   fileInfo.IsDir(),
	}

	return customInfo, nil
}

// ChangeDir 切换目录
func (d *CustomFtpDriver) ChangeDir(path string) error {
	realPath := d.realPath(path)

	if !gfile.Exists(realPath) {
		// 自动创建目录
		err := gfile.Mkdir(realPath)
		if err != nil {
			g.Log().Error(d.ctx, "创建FTP目录失败:", err)
			return err
		}
		g.Log().Info(d.ctx, "自动创建FTP目录:", realPath)
	}

	return nil
}

// ListDir 列出目录内容（使用server.FileInfo接口）
func (d *CustomFtpDriver) ListDir(path string, callback func(server.FileInfo) error) error {
	realPath := d.realPath(path)

	if !gfile.Exists(realPath) {
		return fmt.Errorf("目录不存在: %s", path)
	}

	files, err := os.ReadDir(realPath)
	if err != nil {
		return err
	}

	for _, file := range files {
		info, err := file.Info()
		if err != nil {
			continue
		}

		// 转换为自定义FileInfo
		customInfo := &CustomFileInfo{
			name:    info.Name(),
			size:    info.Size(),
			mode:    info.Mode(),
			modTime: info.ModTime(),
			isDir:   info.IsDir(),
		}

		if err := callback(customInfo); err != nil {
			return err
		}
	}

	return nil
}

// DeleteDir 删除目录
func (d *CustomFtpDriver) DeleteDir(path string) error {
	realPath := d.realPath(path)
	return os.RemoveAll(realPath)
}

// DeleteFile 删除文件
func (d *CustomFtpDriver) DeleteFile(path string) error {
	realPath := d.realPath(path)

	// 5. 删除文件时同步删除数据库记录
	go func() {
		relativePath := strings.TrimPrefix(realPath, d.RootPath)
		relativePath = strings.TrimPrefix(relativePath, string(filepath.Separator))

		var fileInfo modelFile.FileInfo
		result := dao.Db.Where("file_path = ? AND upload_type = ?", relativePath, 2).First(&fileInfo)
		if result.Error == nil {
			dao.Db.Delete(&fileInfo)
			g.Log().Info(d.ctx, "删除FTP文件数据库记录:", fileInfo.FileId)
		}
	}()

	return os.Remove(realPath)
}

// Rename 重命名文件或目录
func (d *CustomFtpDriver) Rename(fromPath, toPath string) error {
	realFromPath := d.realPath(fromPath)
	realToPath := d.realPath(toPath)

	// 6. 重命名文件时更新数据库记录
	go func() {
		oldRelativePath := strings.TrimPrefix(realFromPath, d.RootPath)
		oldRelativePath = strings.TrimPrefix(oldRelativePath, string(filepath.Separator))

		newRelativePath := strings.TrimPrefix(realToPath, d.RootPath)
		newRelativePath = strings.TrimPrefix(newRelativePath, string(filepath.Separator))

		var fileInfo modelFile.FileInfo
		result := dao.Db.Where("file_path = ? AND upload_type = ?", oldRelativePath, 2).First(&fileInfo)
		if result.Error == nil {
			updateData := map[string]interface{}{
				"file_path":  newRelativePath,
				"file_name":  filepath.Base(strings.TrimLeft(toPath, "/")),
				"updated_at": gtime.Now(),
			}
			dao.Db.Model(&fileInfo).Updates(updateData)
			g.Log().Info(d.ctx, "更新FTP文件路径:", fileInfo.FileId)
		}
	}()

	return os.Rename(realFromPath, realToPath)
}

// MakeDir 创建目录
func (d *CustomFtpDriver) MakeDir(path string) error {
	realPath := d.realPath(path)
	return gfile.Mkdir(realPath)
}

// GetFile 下载文件
func (d *CustomFtpDriver) GetFile(path string, offset int64) (int64, io.ReadCloser, error) {
	realPath := d.realPath(path)

	// 7. 记录文件下载次数
	go func() {
		relativePath := strings.TrimPrefix(realPath, d.RootPath)
		relativePath = strings.TrimPrefix(relativePath, string(filepath.Separator))

		var fileInfo modelFile.FileInfo
		result := dao.Db.Where("file_path = ? AND upload_type = ?", relativePath, 2).First(&fileInfo)
		if result.Error == nil {
			dao.Db.Model(&fileInfo).UpdateColumn("download_count", gorm.Expr("download_count + ?", 1))
			g.Log().Info(d.ctx, "FTP文件下载计数:", map[string]interface{}{
				"file_id":   fileInfo.FileId,
				"file_name": fileInfo.FileName,
				"count":     fileInfo.DownloadCount + 1,
			})
		}
	}()

	file, err := os.Open(realPath)
	if err != nil {
		return 0, nil, err
	}

	info, err := file.Stat()
	if err != nil {
		file.Close()
		return 0, nil, err
	}

	if offset > 0 {
		_, err = file.Seek(offset, 0)
		if err != nil {
			file.Close()
			return 0, nil, err
		}
	}

	return info.Size(), file, nil
}

// PutFile 上传文件
func (d *CustomFtpDriver) PutFile(destPath string, data io.Reader, appendData bool) (int64, error) {
	realPath := d.realPath(destPath)

	// 8. 确保目录存在
	dir := filepath.Dir(realPath)
	if !gfile.Exists(dir) {
		err := gfile.Mkdir(dir)
		if err != nil {
			return 0, fmt.Errorf("创建目录失败: %v", err)
		}
	}

	var file *os.File
	var err error

	if appendData {
		file, err = os.OpenFile(realPath, os.O_WRONLY|os.O_CREATE|os.O_APPEND, 0644)
	} else {
		file, err = os.Create(realPath)
	}

	if err != nil {
		return 0, err
	}
	defer file.Close()

	// 9. 写入文件并计算哈希
	hash := sha256.New()
	multiWriter := io.MultiWriter(file, hash)

	written, err := io.Copy(multiWriter, data)
	if err != nil {
		os.Remove(realPath)
		return 0, err
	}

	// 10. 异步保存文件记录到数据库
	go func() {
		fileInfo, err := os.Stat(realPath)
		if err != nil {
			g.Log().Error(d.ctx, "获取FTP上传文件信息失败:", err)
			return
		}

		// 计算相对路径
		relativePath := strings.TrimPrefix(realPath, d.RootPath)
		relativePath = strings.TrimPrefix(relativePath, string(filepath.Separator))

		// 检查文件是否已存在记录
		var existingFile modelFile.FileInfo
		result := dao.Db.Where("file_path = ? AND upload_type = ?", relativePath, 2).First(&existingFile)

		if result.Error != nil {
			// 新文件，创建记录
			fileRecord := &modelFile.FileInfo{
				FileId:        uniqueId.GenerateID(uniqueId.PrefixFile),
				FileName:      filepath.Base(strings.TrimLeft(destPath, "/")),
				FilePath:      relativePath,
				FileSize:      fileInfo.Size(),
				FileType:      d.getFileType(destPath),
				FileExt:       filepath.Ext(destPath),
				FileHash:      hex.EncodeToString(hash.Sum(nil)),
				UploadType:    2,        // FTP上传
				UploadUserId:  d.userId, // 使用真实登录用户ID
				DownloadCount: 0,
				IsPublic:      false,
				ExpireTime:    nil,
				CreatedAt:     gtime.Now(),
				UpdatedAt:     gtime.Now(),
			}

			if err := dao.Db.Create(fileRecord).Error; err != nil {
				g.Log().Error(d.ctx, "保存FTP文件记录失败:", err)
			} else {
				g.Log().Info(d.ctx, "FTP文件上传成功:", map[string]interface{}{
					"file_id":    fileRecord.FileId,
					"file_name":  fileRecord.FileName,
					"file_size":  fileRecord.FileSize,
					"file_path":  fileRecord.FilePath,
					"user_id":    d.userId,
					"user_phone": d.userPhone,
				})
			}
		} else {
			// 文件已存在，更新记录
			updateData := map[string]interface{}{
				"file_size":      fileInfo.Size(),
				"file_hash":      hex.EncodeToString(hash.Sum(nil)),
				"upload_user_id": d.userId, // 更新上传用户ID
				"updated_at":     gtime.Now(),
			}
			dao.Db.Model(&existingFile).Updates(updateData)
			g.Log().Info(d.ctx, "FTP文件更新成功:", map[string]interface{}{
				"file_id":    existingFile.FileId,
				"user_id":    d.userId,
				"user_phone": d.userPhone,
			})
		}
	}()

	return written, nil
}

// 11. realPath 获取真实路径
func (d *CustomFtpDriver) realPath(path string) string {
	return filepath.Join(d.RootPath, path)
}

// 12. getFileType 获取文件类型
func (d *CustomFtpDriver) getFileType(filename string) string {
	ext := strings.ToLower(filepath.Ext(filename))
	switch ext {
	case ".jpg", ".jpeg", ".png", ".gif", ".bmp":
		return "image/" + strings.TrimPrefix(ext, ".")
	case ".mp4", ".avi", ".mov", ".wmv":
		return "video/" + strings.TrimPrefix(ext, ".")
	case ".mp3", ".wav", ".flac":
		return "audio/" + strings.TrimPrefix(ext, ".")
	case ".pdf":
		return "application/pdf"
	case ".txt":
		return "text/plain"
	case ".doc", ".docx":
		return "application/msword"
	default:
		return "application/octet-stream"
	}
}

// CustomDriverFactory 自定义驱动工厂结构体
type CustomDriverFactory struct {
	RootPath string      // FTP根目录路径
	Perm     server.Perm // 权限控制器
	Auth     *CustomAuth // 认证器引用
}

// 13. NewDriver 创建新的FTP驱动器实例
func (f *CustomDriverFactory) NewDriver() (server.Driver, error) {
	driver := &CustomFtpDriver{
		RootPath:  f.RootPath,
		Perm:      f.Perm,
		ctx:       context.Background(),
		userId:    "", // 初始化为空，在认证后设置
		userPhone: "", // 初始化为空，在认证后设置
		auth:      f.Auth,
	}
	return driver, nil
}

// CustomAuth 自定义FTP认证器结构体
type CustomAuth struct {
	ctx     context.Context                // 上下文对象
	userMap map[string]*modelUser.UserInfo // 用户信息缓存
	connMap map[*server.Conn]*CustomFtpDriver
	mu      sync.RWMutex // 读写锁
}

// CheckPasswd 验证用户名和密码
func (auth *CustomAuth) CheckPasswd(name, pass string) (bool, error) {
	// 1. 参数校验
	if name == "" || pass == "" {
		g.Log().Warning(auth.ctx, "FTP登录失败: 用户名或密码为空")
		return false, nil
	}

	// 2. 查询用户信息 - 使用手机号作为用户名
	var userInfo modelUser.UserInfo
	result := dao.Db.Model(&modelUser.UserInfo{}).
		Select("user_id, user_phone, user_pwd, user_nick, user_status, frozen_at, logout_at, deleted_at").
		Where("user_phone = ? AND deleted_at IS NULL", name).
		First(&userInfo)

	// 3. 处理查询结果
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			g.Log().Warning(auth.ctx, "FTP登录失败: 用户不存在", "phone:", name)
			return false, nil
		}
		g.Log().Error(auth.ctx, "FTP登录查询用户失败:", result.Error)
		return false, result.Error
	}

	// 4. 账号状态检查
	// 4.1 检查账号是否已注销
	if userInfo.LogoutAt != nil {
		g.Log().Warning(auth.ctx, "FTP登录失败: 账号已注销", "phone:", name, "logout_time:", userInfo.LogoutAt)
		return false, nil
	}

	// 4.2 检查账号是否已冻结
	if userInfo.FrozenAt != nil {
		g.Log().Warning(auth.ctx, "FTP登录失败: 账号已冻结", "phone:", name, "frozen_time:", userInfo.FrozenAt)
		return false, nil
	}

	// 5. 密码验证 - 使用SHA256加密验证
	encryptedPwd := tools.Sha256Encrypt(pass)
	if encryptedPwd != userInfo.UserPwd {
		g.Log().Warning(auth.ctx, "FTP登录失败: 密码错误", "phone:", name)
		return false, nil
	}

	// 6. 缓存用户信息供驱动器使用
	auth.mu.Lock()
	if auth.userMap == nil {
		auth.userMap = make(map[string]*modelUser.UserInfo)
	}
	auth.userMap[name] = &userInfo
	auth.mu.Unlock()

	g.Log().Info(auth.ctx, "FTP登录成功:", map[string]interface{}{
		"user_id":   userInfo.UserId,
		"user_nick": userInfo.UserNick,
		"phone":     userInfo.UserPhone,
	})

	return true, nil
}

// GetUserInfo 获取用户信息
func (auth *CustomAuth) GetUserInfo(phone string) *modelUser.UserInfo {
	auth.mu.RLock()
	defer auth.mu.RUnlock()

	if auth.userMap != nil {
		return auth.userMap[phone]
	}
	return nil
}

// 14. Start 启动FTP服务器 - 修改为使用数据库认证
func (s *ServerFtpFile) Start(ctx context.Context) error {
	// 从配置文件读取FTP设置
	ftpRoot := g.Cfg().MustGet(ctx, "file.storage_root", "./storage/ftp").String()
	ftpPort := g.Cfg().MustGet(ctx, "ftp.port", 2121).Int()
	ftpHost := g.Cfg().MustGet(ctx, "ftp.hostname", "0.0.0.0").String()

	// 确保FTP根目录存在
	if !gfile.Exists(ftpRoot) {
		if err := gfile.Mkdir(ftpRoot); err != nil {
			return fmt.Errorf("创建FTP根目录失败: %v", err)
		}
	}

	// 创建权限控制器
	perm := server.NewSimplePerm("root", "root")

	// 创建自定义认证器
	customAuth := &CustomAuth{
		ctx:     ctx,
		userMap: make(map[string]*modelUser.UserInfo),
		connMap: make(map[*server.Conn]*CustomFtpDriver),
	}

	// 创建驱动工厂
	factory := &CustomDriverFactory{
		RootPath: ftpRoot,
		Perm:     perm,
		Auth:     customAuth,
	}

	// 配置FTP服务器选项
	opt := &server.ServerOpts{
		Factory:  factory,
		Port:     ftpPort,
		Hostname: ftpHost,
		Auth:     customAuth, // 使用数据库认证
		Logger:   &CustomLogger{ctx: ctx},
		// 性能优化配置
		WelcomeMessage: "欢迎使用AYJ FTP服务器 - 请使用手机号和密码登录",
		PassivePorts:   "30000-30100", // 被动模式端口范围
	}

	// 创建FTP服务器
	s.ftpServer = server.NewServer(opt)

	g.Log().Info(ctx, "FTP服务器启动配置:", map[string]interface{}{
		"host": ftpHost,
		"port": ftpPort,
		"root": ftpRoot,
		"auth": "database",
	})

	// 异步启动FTP服务器
	go func() {
		if err := s.ftpServer.ListenAndServe(); err != nil {
			g.Log().Error(ctx, "FTP服务器启动失败:", err)
		}
	}()

	g.Log().Info(ctx, "FTP服务器启动成功 - 使用数据库用户认证", "端口:", ftpPort)
	return nil
}

// 15. Stop 停止FTP服务器
func (s *ServerFtpFile) Stop(ctx context.Context) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.ftpServer != nil {
		err := s.ftpServer.Shutdown()
		if err != nil {
			g.Log().Error(ctx, "FTP服务器停止失败:", err)
			return err
		}
		s.ftpServer = nil
		g.Log().Info(ctx, "FTP服务器已停止")
	}

	return nil
}

// CustomLogger 自定义日志记录器结构体
type CustomLogger struct {
	ctx context.Context // 上下文对象
}

// 16. Print 打印日志信息
func (l *CustomLogger) Print(sessionId string, message interface{}) {
	g.Log().Info(l.ctx, "FTP操作日志:", map[string]interface{}{
		"session_id": sessionId,
		"message":    message,
	})
}

// Printf 格式化打印日志信息
func (l *CustomLogger) Printf(sessionId string, format string, v ...interface{}) {
	message := fmt.Sprintf(format, v...)
	g.Log().Info(l.ctx, "FTP操作日志:", map[string]interface{}{
		"session_id": sessionId,
		"message":    message,
	})
}

// PrintCommand 打印FTP命令日志
func (l *CustomLogger) PrintCommand(sessionId string, command string, params string) {
	g.Log().Debug(l.ctx, "FTP命令:", map[string]interface{}{
		"session_id": sessionId,
		"command":    command,
		"params":     params,
	})
}

// PrintResponse 打印FTP响应日志
func (l *CustomLogger) PrintResponse(sessionId string, code int, message string) {
	g.Log().Debug(l.ctx, "FTP响应:", map[string]interface{}{
		"session_id": sessionId,
		"code":       code,
		"message":    message,
	})
}

// 17. GetFtpFileList 获取FTP文件列表
func (s *ServerFtpFile) GetFtpFileList(ctx context.Context, page, pageSize int) ([]modelFile.FileInfo, int64, error) {
	var files []modelFile.FileInfo
	var total int64

	// 查询FTP上传的文件
	query := dao.Db.Model(&modelFile.FileInfo{}).Where("upload_type = ?", 2)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&files).Error; err != nil {
		return nil, 0, err
	}

	return files, total, nil
}

// 18. CleanOrphanRecords 清理FTP孤立文件记录
func (s *ServerFtpFile) CleanOrphanRecords(ctx context.Context) error {
	ftpRoot := g.Cfg().MustGet(ctx, "ftp.root", "./storage/ftp").String()

	var files []modelFile.FileInfo
	err := dao.Db.Where("upload_type = ?", 2).Find(&files).Error
	if err != nil {
		return err
	}

	var orphanIds []string
	for _, file := range files {
		realPath := filepath.Join(ftpRoot, file.FilePath)
		if !gfile.Exists(realPath) {
			orphanIds = append(orphanIds, file.FileId)
		}
	}

	if len(orphanIds) > 0 {
		err = dao.Db.Where("file_id IN ?", orphanIds).Delete(&modelFile.FileInfo{}).Error
		if err != nil {
			return err
		}
		g.Log().Info(ctx, "清理FTP孤立记录:", len(orphanIds))
	}

	return nil
}
