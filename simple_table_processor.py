#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版Word表格处理脚本
使用内置库处理，无需额外依赖
"""

import os
import sys

def check_requirements():
    """
    1. 检查运行环境和依赖
    """
    print("=" * 50)
    print("Word表格批量复制工具 - 简化版")
    print("=" * 50)
    
    # 2. 检查输入文件
    input_file = 'internal/model/user/word.docx'
    if not os.path.exists(input_file):
        print(f"✗ 错误: 未找到输入文件 {input_file}")
        print("  请确保文件存在且路径正确")
        return False
    
    print(f"✓ 找到输入文件: {input_file}")
    
    # 3. 检查python-docx库
    try:
        import docx
        print("✓ python-docx库可用")
        return True
    except ImportError:
        print("✗ 缺少python-docx库")
        print("  正在尝试自动安装...")
        return install_docx()

def install_docx():
    """
    4. 自动安装python-docx库
    """
    try:
        import subprocess
        import sys
        
        # 5. 尝试安装
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', 'python-docx'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ python-docx库安装成功")
            return True
        else:
            print(f"✗ 安装失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"✗ 安装过程出错: {e}")
        return False

def create_manual_instructions():
    """
    6. 创建手动操作说明
    """
    instructions = """
# 手动操作说明

由于自动处理遇到问题，请按以下步骤手动操作：

## 步骤1：安装Python依赖
在命令行中运行：
```
pip install python-docx
```

## 步骤2：表格布局设计
根据您的需求，在Word中手动创建2列4行的表格布局：

### A4页面设置
- 页面边距：上下左右均为0.26cm（约10像素）
- 页面方向：纵向
- 纸张大小：A4 (21cm × 29.7cm)

### 表格尺寸计算
- 可用页面宽度：21 - 0.52 = 20.48cm
- 可用页面高度：29.7 - 0.52 = 29.18cm
- 每个表格宽度：(20.48 - 0.3) / 2 = 10.09cm
- 每个表格高度：(29.18 - 0.9) / 4 = 7.07cm

### 布局方案
1. 创建一个2列1行的主表格
2. 在每个单元格中插入您的原始表格
3. 重复4次，形成2列4行的布局
4. 调整表格间距确保对称

## 步骤3：精确设置
1. 选中整个文档
2. 设置表格属性：
   - 表格宽度：10.09cm
   - 表格高度：7.07cm
   - 对齐方式：居中
3. 设置行间距：0.3cm
4. 设置列间距：0.3cm

## 步骤4：验证布局
- 打印预览检查是否对称
- 确保每个表格大小一致
- 验证边距设置正确

## 步骤5：打印和裁剪
1. 使用A4纸打印，100%缩放
2. 沿中线对折
3. 按行裁剪，每次可得到2个表格
4. 总共4次裁剪得到8个相同表格

## 注意事项
- 保持原始表格格式不变
- 确保所有表格内容完整
- 打印前检查页面设置
- 使用锋利的裁纸刀确保裁剪整齐
"""
    
    # 7. 保存说明文件
    with open('手动操作说明.txt', 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    print("✓ 已生成手动操作说明文件：手动操作说明.txt")

def main():
    """
    8. 主程序入口
    """
    if not check_requirements():
        print("\n由于环境问题，无法自动处理。")
        print("正在生成手动操作说明...")
        create_manual_instructions()
        print("\n请查看生成的说明文件，按步骤手动操作。")
        return
    
    # 9. 如果环境正常，运行主处理脚本
    try:
        from process_word_table import process_word_table
        success = process_word_table()
        if success:
            print("\n✓ 自动处理完成！")
        else:
            print("\n✗ 自动处理失败，请查看手动操作说明。")
            create_manual_instructions()
    except Exception as e:
        print(f"\n✗ 处理过程出错: {e}")
        print("正在生成手动操作说明...")
        create_manual_instructions()

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
