/*
******		FileName	:	userInfo.go
******		Describe	:	此文件主要用于用户个人信息的管理
******		Date		:	2024-11-12
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:   用户个人信息
 */

package user

import (
	"ayj_file_back/api/user"
	"ayj_file_back/internal/public/response"
	"ayj_file_back/internal/service/user"
	"context"
	"github.com/gogf/gf/v2/frame/g"
)

type CtrlUserInfo struct {
	server *server.ServerUserInfo
}

var CtrlUserInfoApi = CtrlUserInfo{}

// 获取用户基本信息
func (c *CtrlUserInfo) GetUserInfo(ctx context.Context, req *user.UserInfoReq) (res *user.UserInfoRes, err error) {
	res, err = c.server.GetUserInfo(g.Map{"user_id": req.UserId})
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// 修改用户基本信息
func (c *CtrlUserInfo) UpdateUserInfo(ctx context.Context, req *user.UserInfoUpdateReq) (res *user.UserInfoRes, err error) {
	// 传递上下文参数到服务层
	res, err = c.server.UpdateUserInfo(ctx, req)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}

	return
}

// 修改绑定手机
func (c *CtrlUserInfo) ModifyUserPhpne(ctx context.Context, req *user.UserPhoneModifyReq) (res *user.UserInfoRes, err error) {
	res, err = c.server.ModifyUserPhone(req)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}

	return
}

// 搜索用户
func (c *CtrlUserInfo) SearchUser(ctx context.Context, req *user.SearchUserReq) (res *user.UserInfoRes, err error) {
	res, err = c.server.SearchUser(req)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}

	return
}
