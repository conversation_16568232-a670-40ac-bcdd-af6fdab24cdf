# Word表格2列4行布局操作指南

## 目标
将 `internal/model/user/word.docx` 中的表格复制成8份，排列为2列4行，适合A4纸打印和对折裁剪。

## 精确参数设置

### 1. 页面设置
- **纸张大小**: A4 (21cm × 29.7cm)
- **页面方向**: 纵向
- **页边距**: 上下左右均为 **0.26cm** (约10像素)

### 2. 布局计算
- **可用页面宽度**: 21 - 0.52 = **20.48cm**
- **可用页面高度**: 29.7 - 0.52 = **29.18cm**
- **列间距**: **0.3cm**
- **行间距**: **0.3cm**
- **每个表格宽度**: (20.48 - 0.3) ÷ 2 = **10.09cm**
- **每个表格高度**: (29.18 - 0.9) ÷ 4 = **7.07cm**

## 详细操作步骤

### 步骤1：创建新文档
1. 打开Microsoft Word
2. 创建新的空白文档
3. 设置页面布局：
   - 点击"布局" → "页边距" → "自定义边距"
   - 上边距：0.26cm
   - 下边距：0.26cm
   - 左边距：0.26cm
   - 右边距：0.26cm
   - 点击"确定"

### 步骤2：创建主布局表格
1. 插入表格：
   - 点击"插入" → "表格"
   - 选择"2列1行"
2. 设置表格属性：
   - 右键点击表格 → "表格属性"
   - 在"表格"选项卡中：
     - 宽度：20.48cm
     - 对齐方式：居中
   - 在"列"选项卡中：
     - 第1列宽度：10.09cm
     - 第2列宽度：10.09cm

### 步骤3：复制原始表格
1. 打开原始文件 `internal/model/user/word.docx`
2. 选中整个表格（点击表格左上角的十字图标）
3. 复制表格（Ctrl+C）
4. 回到新文档
5. 在第一个单元格中粘贴（Ctrl+V）
6. 在第二个单元格中再次粘贴

### 步骤4：调整表格尺寸
1. 选中第一个单元格中的表格
2. 右键 → "表格属性"
3. 设置：
   - 宽度：10.09cm
   - 高度：7.07cm
   - 对齐方式：居中
4. 对第二个单元格中的表格重复相同操作

### 步骤5：复制整行
1. 选中整个主表格行
2. 复制（Ctrl+C）
3. 在表格下方粘贴3次，形成4行
4. 在每次粘贴后，按Enter键添加行间距

### 步骤6：精确调整间距
1. 选中第1行和第2行之间的段落
2. 右键 → "段落"
3. 设置段后间距：0.3cm
4. 对其他行间距重复此操作

### 步骤7：最终检查
1. 确保所有8个表格尺寸一致
2. 检查表格内容完整
3. 验证页面布局对称

## 布局示意图

```
┌─────────────────────────────────────────┐ ← 0.26cm边距
│ 0.26cm                        0.26cm   │
│ ┌─────────┐ 0.3cm ┌─────────┐          │
│ │ 表格1   │       │ 表格2   │          │
│ │10.09cm  │       │10.09cm  │          │
│ │×7.07cm  │       │×7.07cm  │          │
│ └─────────┘       └─────────┘          │
│                                        │ ← 0.3cm行间距
│ ┌─────────┐       ┌─────────┐          │
│ │ 表格3   │       │ 表格4   │          │
│ └─────────┘       └─────────┘          │
│                                        │
│ ┌─────────┐       ┌─────────┐          │
│ │ 表格5   │       │ 表格6   │          │
│ └─────────┘       └─────────┘          │
│                                        │
│ ┌─────────┐       ┌─────────┐          │
│ │ 表格7   │       │ 表格8   │          │
│ └─────────┘       └─────────┘          │
│ 0.26cm                        0.26cm   │
└─────────────────────────────────────────┘ ← 0.26cm边距
```

## 打印设置

### 打印前检查
1. 点击"文件" → "打印预览"
2. 确认：
   - 页面显示完整
   - 表格排列整齐
   - 边距设置正确

### 打印参数
- **纸张**: A4
- **缩放**: 100%（无缩放）
- **页面设置**: 实际尺寸
- **质量**: 高质量

## 裁剪指南

### 对折裁剪法
1. **第一次对折**: 沿页面中央垂直线对折
2. **第一次裁剪**: 沿第1行和第2行之间的水平线裁剪
   - 得到：表格1、表格2、表格3、表格4
3. **第二次裁剪**: 沿第2行和第3行之间的水平线裁剪
   - 得到：表格5、表格6
4. **第三次裁剪**: 沿第3行和第4行之间的水平线裁剪
   - 得到：表格7、表格8

### 裁剪工具
- 使用锋利的裁纸刀
- 配合直尺确保裁剪直线
- 在切割垫上操作保护桌面

## 质量检查

### 尺寸验证
- 每个表格应为：10.09cm × 7.07cm
- 使用尺子测量确认尺寸一致

### 内容检查
- 所有表格内容完整
- 字体格式保持一致
- 表格边框清晰

## 故障排除

### 常见问题
1. **表格变形**: 检查表格属性设置是否正确
2. **内容不完整**: 确保复制时选中了完整表格
3. **间距不均**: 重新设置段落间距
4. **打印偏移**: 检查打印机页边距设置

### 解决方案
- 使用"表格属性"精确设置尺寸
- 使用"段落格式"控制间距
- 打印前使用"打印预览"检查

## 保存建议
- 保存为 `.docx` 格式
- 文件名建议：`表格8份布局_2列4行.docx`
- 备份原始文件
