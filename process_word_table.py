#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Word表格处理脚本 - 性能优先版本
功能：将单个表格复制成2列4行的布局，适合A4纸打印和对折裁剪
"""

from docx import Document
from docx.shared import Inches, Pt, Cm
from docx.enum.section import WD_SECTION
from docx.enum.table import WD_TABLE_ALIGNMENT
from docx.enum.text import WD_ALIGN_PARAGRAPH
import copy

def process_word_table():
    """
    主处理函数
    1. 读取原始Word文档中的表格
    2. 创建新文档，设置A4页面边距为10像素
    3. 将表格复制8份，排列成2列4行
    4. 精确计算间距确保完美对称
    """

    # 1. 读取原始文档
    try:
        doc = Document('internal/model/user/word.docx')
        print("✓ 成功读取原始文档")
    except Exception as e:
        print(f"✗ 读取文档失败: {e}")
        return False

    # 2. 获取第一个表格作为模板
    if not doc.tables:
        print("✗ 文档中没有找到表格")
        return False

    original_table = doc.tables[0]
    print(f"✓ 找到表格，行数: {len(original_table.rows)}, 列数: {len(original_table.columns)}")

    # 3. 创建新文档并设置页面参数
    new_doc = Document()
    section = new_doc.sections[0]

    # 4. 设置精确的页面边距 (10像素 = 0.26cm ≈ 0.104英寸)
    margin_size = Cm(0.26)  # 10像素转换为厘米
    section.top_margin = margin_size
    section.bottom_margin = margin_size
    section.left_margin = margin_size
    section.right_margin = margin_size

    # 5. 计算A4页面可用空间 (精确计算)
    # A4纸尺寸: 21cm x 29.7cm
    page_width_cm = 21.0 - 0.52  # 减去左右边距
    page_height_cm = 29.7 - 0.52  # 减去上下边距

    # 6. 计算表格布局参数 (2列4行，完美对称)
    col_spacing = 0.3  # 列间距 (cm)
    row_spacing = 0.3  # 行间距 (cm)

    table_width_cm = (page_width_cm - col_spacing) / 2
    table_height_cm = (page_height_cm - 3 * row_spacing) / 4

    print(f"✓ 计算布局参数:")
    print(f"  - 页面可用空间: {page_width_cm:.1f}cm x {page_height_cm:.1f}cm")
    print(f"  - 每个表格尺寸: {table_width_cm:.1f}cm x {table_height_cm:.1f}cm")
    print(f"  - 间距设置: 列间距{col_spacing}cm, 行间距{row_spacing}cm")

    # 7. 创建精确的2x4表格布局
    for row_idx in range(4):
        print(f"  正在创建第 {row_idx + 1} 行...")

        # 8. 创建包含2个表格的行容器
        main_table = new_doc.add_table(rows=1, cols=2)
        main_table.alignment = WD_TABLE_ALIGNMENT.CENTER

        # 9. 设置主表格宽度和列宽
        main_table.width = Cm(page_width_cm)
        main_table.columns[0].width = Cm(table_width_cm)
        main_table.columns[1].width = Cm(table_width_cm)

        # 10. 在每个单元格中复制原始表格
        for col_idx in range(2):
            cell = main_table.cell(0, col_idx)
            table_num = row_idx * 2 + col_idx + 1

            # 11. 复制表格内容到单元格
            success = copy_table_to_cell(cell, original_table, table_width_cm, table_height_cm)
            if success:
                print(f"    ✓ 表格 {table_num} 复制完成")
            else:
                print(f"    ✗ 表格 {table_num} 复制失败")

        # 12. 添加行间距（除了最后一行）
        if row_idx < 3:
            spacing_para = new_doc.add_paragraph()
            spacing_para.space_after = Pt(row_spacing * 28.35)  # cm转pt

    # 13. 保存新文档
    output_filename = 'internal/model/user/word_8_copies_layout.docx'
    new_doc.save(output_filename)
    print(f"✓ 处理完成！新文档已保存为: {output_filename}")
    print(f"✓ 文档包含8个相同表格，排列为2列4行，适合对折裁剪")
    return True

def copy_table_to_cell(cell, original_table, target_width_cm, target_height_cm):
    """
    高性能表格复制函数
    将原始表格精确复制到指定单元格中，保持格式和尺寸
    """
    try:
        # 1. 清空单元格内容
        cell.text = ""

        # 2. 在单元格中创建新表格
        new_table = cell.add_table(
            rows=len(original_table.rows),
            cols=len(original_table.columns)
        )

        # 3. 设置表格尺寸
        new_table.width = Cm(target_width_cm)

        # 4. 批量复制表格内容和格式
        for i, orig_row in enumerate(original_table.rows):
            new_row = new_table.rows[i]
            # 设置行高
            new_row.height = Cm(target_height_cm / len(original_table.rows))

            for j, orig_cell in enumerate(orig_row.cells):
                new_cell = new_table.cell(i, j)

                # 5. 复制单元格内容
                copy_cell_content(orig_cell, new_cell)

                # 6. 设置列宽
                if i == 0:  # 只在第一行设置列宽
                    new_table.columns[j].width = Cm(target_width_cm / len(original_table.columns))

        return new_table

    except Exception as e:
        print(f"✗ 复制表格时出错: {e}")
        return None

def copy_cell_content(source_cell, target_cell):
    """
    复制单元格内容和格式的优化函数
    """
    try:
        # 1. 清空目标单元格
        target_cell.text = ""

        # 2. 复制所有段落
        for para_idx, source_para in enumerate(source_cell.paragraphs):
            if para_idx == 0:
                target_para = target_cell.paragraphs[0]
            else:
                target_para = target_cell.add_paragraph()

            # 3. 设置段落对齐方式
            target_para.alignment = source_para.alignment

            # 4. 复制文本运行和格式
            target_para.text = ""  # 清空默认文本
            for run in source_para.runs:
                new_run = target_para.add_run(run.text)

                # 5. 复制字体格式
                if run.font.name:
                    new_run.font.name = run.font.name
                if run.font.size:
                    new_run.font.size = run.font.size
                new_run.bold = run.bold
                new_run.italic = run.italic
                new_run.underline = run.underline

        return True

    except Exception as e:
        print(f"✗ 复制单元格内容时出错: {e}")
        return False

def main():
    """
    主程序入口
    """
    print("=" * 50)
    print("Word表格批量复制工具")
    print("功能：将单个表格复制成2列4行布局，适合A4打印对折裁剪")
    print("=" * 50)

    try:
        success = process_word_table()
        if success:
            print("\n" + "=" * 50)
            print("✓ 处理成功完成！")
            print("✓ 请检查生成的文档：internal/model/user/word_8_copies_layout.docx")
            print("✓ 该文档已优化为A4纸打印，边距10像素，完美对称布局")
            print("✓ 打印后可直接对折裁剪，每个表格尺寸完全一致")
            print("=" * 50)
        else:
            print("\n" + "=" * 50)
            print("✗ 处理过程中出现错误，请检查:")
            print("  1. 确保 internal/model/user/word.docx 文件存在")
            print("  2. 确保文档中包含至少一个表格")
            print("  3. 确保有足够的文件写入权限")
            print("=" * 50)
    except Exception as e:
        print(f"\n✗ 程序执行出错: {e}")

if __name__ == "__main__":
    main()
