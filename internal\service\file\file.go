/*
******		FileName	:	file.go
******		Describe	:	此文件主要用于文件管理服务实现
******		Date		:	2024-04-03
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:   文件服务相关的业务逻辑实现
 */

package server

import (
	"ayj_file_back/api/file"
	"ayj_file_back/internal/dao"
	modelFile "ayj_file_back/internal/model/file"
	"ayj_file_back/internal/public/tools"
	"ayj_file_back/internal/public/uniqueId"
	"context"
	"crypto/sha256"
	"encoding/hex"
	"errors"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/jlaffaye/ftp"
	"gorm.io/gorm"
)

type ServerFile struct {
}

var rootDir string //	文件保存根目录

func init() {
	rootDir = getFileStorageRoot()
}

// 1. 获取文件存储根目录
func getFileStorageRoot() string {
	rootDir := g.Cfg().MustGet(context.Background(), "file.storage_root", "./storage/files").String()
	if !gfile.Exists(rootDir) {
		err := gfile.Mkdir(rootDir)
		if err != nil {
			g.Log().Error(context.Background(), "创建文件存储根目录失败:", err)
		}
	}
	return rootDir
}

// 1.1 获取临时文件目录
func (s *ServerFile) getTempDir() string {
	tempDir := g.Cfg().MustGet(context.Background(), "file.temp_dir", "./storage/temp").String()
	if !gfile.Exists(tempDir) {
		err := gfile.Mkdir(tempDir)
		if err != nil {
			g.Log().Error(context.Background(), "创建临时文件目录失败:", err)
		}
	}
	return tempDir
}

// 1.2 获取分片大小配置
func (s *ServerFile) getChunkSize() int64 {
	return g.Cfg().MustGet(context.Background(), "http.chunk_size", 1048576).Int64()
}

// 1.3 获取断点续传超时时间
func (s *ServerFile) getResumeTimeout() int64 {
	return g.Cfg().MustGet(context.Background(), "http.resume_timeout", 3600).Int64()
}

// 2. 生成文件存储路径（修复版本）
func (s *ServerFile) generateFilePath() (_strFileSavePath string, _strFileId string) {
	// 1. 生成日期目录
	dateDir := time.Now().Format("2006/01/02")

	// 2. 生成唯一前缀避免重名
	uniquePrefix := uniqueId.GenerateID(uniqueId.PrefixFile)

	// 3.返回路径
	return filepath.Join(dateDir, uniquePrefix), uniquePrefix
}

// 清理文件名中的特殊字符
func (s *ServerFile) sanitizeFileName(fileName string) string {
	// 移除或替换特殊字符
	fileName = strings.ReplaceAll(fileName, " ", "_")
	fileName = strings.ReplaceAll(fileName, "(", "")
	fileName = strings.ReplaceAll(fileName, ")", "")
	fileName = strings.ReplaceAll(fileName, "[", "")
	fileName = strings.ReplaceAll(fileName, "]", "")

	// 限制文件名长度
	if len(fileName) > 100 {
		ext := filepath.Ext(fileName)
		base := strings.TrimSuffix(fileName, ext)
		fileName = base[:100-len(ext)] + ext
	}

	return fileName
}

// 3. 计算文件哈希值（增强错误处理）
func (s *ServerFile) calculateFileHash(filePath string) (string, error) {
	// 1. 检查文件是否存在
	if !gfile.Exists(filePath) {
		return "", fmt.Errorf("文件不存在: %s", filePath)
	}

	// 2. 检查是否为文件（不是目录）
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		return "", fmt.Errorf("获取文件信息失败: %v", err)
	}
	if fileInfo.IsDir() {
		return "", fmt.Errorf("路径是目录而不是文件: %s", filePath)
	}

	// 3. 打开文件
	file, err := os.Open(filePath)
	if err != nil {
		return "", fmt.Errorf("打开文件失败: %v", err)
	}
	defer file.Close()

	// 4. 计算哈希值
	hash := sha256.New()
	if _, err := io.Copy(hash, file); err != nil {
		return "", fmt.Errorf("读取文件内容失败: %v", err)
	}

	return hex.EncodeToString(hash.Sum(nil)), nil
}

// 4. HTTP文件上传（优化版本）
func (s *ServerFile) UploadFile(ctx context.Context, uploadFile *ghttp.UploadFile, req *file.UploadFileReq) (res *file.UploadFileRes, err error) {
	res = &file.UploadFileRes{}

	// 1. 先计算上传文件的哈希值（在内存中）
	fileHash, _, err := s.calculateUploadFileHash(uploadFile)
	if err != nil {
		return nil, fmt.Errorf("计算文件哈希值失败: %v", err)
	}

	// 2. 检查重复文件（在保存前检查）
	var existingFile modelFile.FileInfo
	result := dao.Db.Where("file_hash = ?", fileHash).First(&existingFile)
	if result.Error == nil {
		g.Log().Info(ctx, "文件重复，直接返回:", uploadFile.Filename, fileHash)

		res.FileId = existingFile.FileId
		res.FileName = existingFile.FileName
		res.FileSize = existingFile.FileSize
		res.FileType = existingFile.FileType
		res.FileUrl = "/file/download?file_id=" + existingFile.FileId
		res.IsExist = true
		res.IsComplete = true

		return res, nil
	}

	// 3. 文件不重复，开始保存
	relativePath, fileId := s.generateFilePath()
	if relativePath == "" {
		return nil, errors.New("生成文件存储路径失败")
	}

	absolutePath := filepath.Join(rootDir, relativePath)

	// 4. 确保父目录存在
	parentDir := filepath.Dir(absolutePath)
	if !gfile.Exists(parentDir) {
		if err = gfile.Mkdir(parentDir); err != nil {
			return nil, fmt.Errorf("创建目录失败: %v", err)
		}
	}

	// 5. 保存文件
	if _, err = uploadFile.Save(absolutePath); err != nil {
		return nil, fmt.Errorf("保存文件失败: %v", err)
	}

	// 6. 验证文件保存
	if !gfile.Exists(absolutePath) {
		return nil, errors.New("文件保存失败")
	}

	// 7. 处理GoFrame保存为目录的问题
	fileInfo, err := os.Stat(absolutePath)
	if err != nil {
		os.Remove(absolutePath)
		return nil, fmt.Errorf("获取文件信息失败: %v", err)
	}

	if fileInfo.IsDir() {
		absolutePath, err = s.handleDirectorySave(absolutePath, uploadFile.Filename)
		if err != nil {
			return nil, err
		}

		// 重新获取文件信息
		fileInfo, err = os.Stat(absolutePath)
		if err != nil {
			return nil, fmt.Errorf("获取文件信息失败: %v", err)
		}
	}

	// 8. 保存文件记录
	now := gtime.Now()

	fileRecord := &modelFile.FileInfo{
		FileId:        fileId,
		FileName:      uploadFile.Filename,
		FilePath:      strings.Replace(absolutePath, rootDir+string(filepath.Separator), "", 1),
		FileSize:      fileInfo.Size(),
		FileType:      req.FileType,
		FileExt:       filepath.Ext(uploadFile.Filename),
		FileHash:      fileHash,
		UploadType:    1,
		UploadUserId:  tools.GetUserIdFromCtx(ctx),
		DownloadCount: 0,
		IsPublic:      req.IsPublic,
		ExpireTime:    nil,
		CreatedAt:     now,
		UpdatedAt:     now,
	}

	if err = dao.Db.Create(fileRecord).Error; err != nil {
		_ = os.Remove(absolutePath)
		return nil, fmt.Errorf("保存文件记录失败: %v", err)
	}

	// 9. 设置响应
	res.FileId = fileId
	res.FileName = uploadFile.Filename
	res.FileSize = fileInfo.Size()
	res.FileType = req.FileType
	res.FileUrl = "/file/download?file_id=" + fileId
	res.IsExist = false
	res.IsComplete = true

	g.Log().Info(ctx, "文件上传成功:", map[string]interface{}{
		"file_id":   fileId,
		"file_name": uploadFile.Filename,
		"file_size": fileInfo.Size(),
	})

	return res, nil
}

// 计算上传文件的哈希值（在内存中）
func (s *ServerFile) calculateUploadFileHash(uploadFile *ghttp.UploadFile) (string, int64, error) {
	// 1. 打开上传文件
	fileInfo, err := uploadFile.Open()
	if err != nil {
		return "", 0, fmt.Errorf("打开上传文件失败: %v", err)
	}
	defer fileInfo.Close()

	// 2. 计算哈希值和文件大小
	hash := sha256.New()
	size, err := io.Copy(hash, fileInfo)
	if err != nil {
		return "", 0, fmt.Errorf("读取文件内容失败: %v", err)
	}

	return hex.EncodeToString(hash.Sum(nil)), size, nil
}

// 处理GoFrame保存为目录的问题
func (s *ServerFile) handleDirectorySave(dirPath, originalFileName string) (string, error) {
	// 1. 查找目录下的文件
	files, err := os.ReadDir(dirPath)
	if err != nil || len(files) == 0 {
		os.RemoveAll(dirPath)
		return "", errors.New("未找到上传的文件")
	}

	// 2. 移动文件到正确位置
	actualFile := filepath.Join(dirPath, files[0].Name())
	newPath := dirPath + "_" + originalFileName

	err = os.Rename(actualFile, newPath)
	if err != nil {
		os.RemoveAll(dirPath)
		return "", fmt.Errorf("移动文件失败: %v", err)
	}

	// 3. 删除空目录
	os.RemoveAll(dirPath)

	return newPath, nil
}

// 5. 获取文件信息
func (s *ServerFile) GetFileInfo(ctx context.Context, fileId string) (*modelFile.FileInfo, error) {
	var fileInfo modelFile.FileInfo
	result := dao.Db.Unscoped().Where("file_id = ?", fileId).First(&fileInfo)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, errors.New("文件不存在")
		}
		return nil, fmt.Errorf("查询文件信息失败: %v", result.Error)
	}
	if fileInfo.DeletedAt != nil {
		return nil, errors.New("文件已删除")
	}

	return &fileInfo, nil
}

// 6. HTTP文件下载
func (s *ServerFile) DownloadFile(ctx context.Context, absolutePath string, writer io.Writer) error {
	if !gfile.Exists(absolutePath) {
		return errors.New("文件不存在")
	}

	file, err := os.Open(absolutePath)
	if err != nil {
		return fmt.Errorf("打开文件失败: %v", err)
	}
	defer file.Close()

	buffer := make([]byte, 4096)
	_, err = io.CopyBuffer(writer, file, buffer)
	if err != nil {
		return fmt.Errorf("文件传输失败: %v", err)
	}

	// 异步更新下载次数
	go func(path string) {
		var fileInfo modelFile.FileInfo
		dao.Db.Where("file_path = ?", path).First(&fileInfo)
		dao.Db.Model(&fileInfo).UpdateColumn("download_count", gorm.Expr("download_count + ?", 1))
	}(absolutePath)

	return nil
}

// 7. 获取文件列表
func (s *ServerFile) GetFileList(ctx context.Context, req *file.GetFileListReq) (res *file.GetFileListRes, err error) {
	res = &file.GetFileListRes{}

	query := dao.Db.Model(&modelFile.FileInfo{})

	if req.FileName != "" {
		query = query.Where("file_name LIKE ?", "%"+req.FileName+"%")
	}

	if req.FileType != "" {
		query = query.Where("file_type = ?", req.FileType)
	}

	if req.UploadType > 0 {
		query = query.Where("upload_type = ?", req.UploadType)
	}

	if req.UploadUserId != "" {
		query = query.Where("upload_user_id = ?", req.UploadUserId)
	}

	var total int64
	if err = query.Count(&total).Error; err != nil {
		return nil, err
	}
	res.Total = total

	page := req.Page
	if page < 1 {
		page = 1
	}

	size := req.Size
	if size < 1 || size > 100 {
		size = 20
	}

	offset := (page - 1) * size

	orderBy := "created_at DESC"
	if req.OrderBy != "" {
		orderBy = req.OrderBy
	}

	var files []modelFile.FileInfo
	if err = query.Order(orderBy).Offset(offset).Limit(size).Find(&files).Error; err != nil {
		return nil, err
	}

	res.List = make([]*file.FileInfoItem, 0, len(files))
	for _, f := range files {
		item := &file.FileInfoItem{
			FileId:        f.FileId,
			FileName:      f.FileName,
			FileSize:      f.FileSize,
			FileType:      f.FileType,
			FileExt:       f.FileExt,
			UploadType:    f.UploadType,
			UploadUserId:  f.UploadUserId,
			DownloadCount: f.DownloadCount,
			IsPublic:      f.IsPublic,
			UploadTime:    f.CreatedAt.String(),
		}

		if f.ExpireTime != nil {
			item.ExpireTime = f.ExpireTime.String()
		}

		res.List = append(res.List, item)
	}

	return res, nil
}

// 8. 删除文件
func (s *ServerFile) DeleteFile(ctx context.Context, fileId string) (res *file.DeleteFileRes, err error) {
	res = &file.DeleteFileRes{}

	fileInfo, err := s.GetFileInfo(ctx, fileId)
	if err != nil {
		return nil, err
	}

	tx := dao.Db.Begin()

	if err = tx.Where("file_id = ?", fileId).Delete(&modelFile.FileInfo{}).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("删除文件记录失败: %v", err)
	}

	if err = tx.Where("file_id = ?", fileId).Delete(&modelFile.FileChunk{}).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("删除分片记录失败: %v", err)
	}

	if err = tx.Where("file_id = ?", fileId).Delete(&modelFile.FtpTransfer{}).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("删除FTP传输记录失败: %v", err)
	}

	if err = tx.Where("file_id = ?", fileId).Delete(&modelFile.FileAccess{}).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("删除访问权限记录失败: %v", err)
	}

	if err = tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("提交事务失败: %v", err)
	}

	absolutePath := filepath.Join(rootDir, fileInfo.FilePath)
	if gfile.Exists(absolutePath) {
		if err = os.Remove(absolutePath); err != nil {
			g.Log().Warning(ctx, "删除物理文件失败:", err)
		}
	}

	res.Success = true
	return res, nil
}

// 9. FTP连接
func (s *ServerFile) connectFTP(server string, port int, username, password string) (*ftp.ServerConn, error) {
	addr := fmt.Sprintf("%s:%d", server, port)
	conn, err := ftp.Dial(addr, ftp.DialWithTimeout(30*time.Second))
	if err != nil {
		return nil, fmt.Errorf("连接FTP服务器失败: %v", err)
	}

	if err = conn.Login(username, password); err != nil {
		conn.Quit()
		return nil, fmt.Errorf("FTP登录失败: %v", err)
	}

	return conn, nil
}

// 10. FTP文件上传
func (s *ServerFile) UploadFtpFile(ctx context.Context, req *file.UploadFtpFileReq) (res *file.UploadFtpFileRes, err error) {
	res = &file.UploadFtpFileRes{}

	fileInfo, err := s.GetFileInfo(ctx, req.FileId)
	if err != nil {
		return nil, err
	}

	transferId := uniqueId.GenerateID("ftp")
	now := gtime.Now()

	ftpTransfer := &modelFile.FtpTransfer{
		TransferId:   transferId,
		FileId:       req.FileId,
		TransferType: 1,
		FtpServer:    req.FtpServer,
		FtpPort:      req.FtpPort,
		FtpPath:      req.FtpPath,
		Status:       0,
		UserId:       tools.GetUserIdFromCtx(ctx),
		StartTime:    now,
		CreatedAt:    now,
		UpdatedAt:    now,
	}

	if err = dao.Db.Create(ftpTransfer).Error; err != nil {
		return nil, fmt.Errorf("创建FTP传输记录失败: %v", err)
	}

	// 异步执行FTP上传
	go func() {
		var conn *ftp.ServerConn
		var localFile *os.File
		var updateData = map[string]interface{}{
			"end_time":   gtime.Now(),
			"updated_at": gtime.Now(),
		}

		defer func() {
			dao.Db.Model(&modelFile.FtpTransfer{}).Where("transfer_id = ?", transferId).Updates(updateData)

			if localFile != nil {
				localFile.Close()
			}
			if conn != nil {
				conn.Quit()
			}
		}()

		conn, err = s.connectFTP(req.FtpServer, req.FtpPort, req.FtpUsername, req.FtpPassword)
		if err != nil {
			updateData["status"] = 2
			updateData["error_msg"] = err.Error()
			return
		}

		localFilePath := filepath.Join(rootDir, fileInfo.FilePath)
		localFile, err = os.Open(localFilePath)
		if err != nil {
			updateData["status"] = 2
			updateData["error_msg"] = fmt.Sprintf("打开本地文件失败: %v", err)
			return
		}

		err = conn.Stor(req.FtpPath, localFile)
		if err != nil {
			updateData["status"] = 2
			updateData["error_msg"] = fmt.Sprintf("FTP上传失败: %v", err)
			return
		}

		updateData["status"] = 1
	}()

	res.TransferId = transferId
	res.Status = "进行中"

	return res, nil
}

// 11. FTP文件下载
func (s *ServerFile) DownloadFtpFile(ctx context.Context, req *file.DownloadFtpFileReq) (res *file.DownloadFtpFileRes, err error) {
	res = &file.DownloadFtpFileRes{}

	transferId := uniqueId.GenerateID("ftp")
	now := gtime.Now()

	ftpTransfer := &modelFile.FtpTransfer{
		TransferId:   transferId,
		TransferType: 2,
		FtpServer:    req.FtpServer,
		FtpPort:      req.FtpPort,
		FtpPath:      req.FtpPath,
		Status:       0,
		UserId:       tools.GetUserIdFromCtx(ctx),
		StartTime:    now,
		CreatedAt:    now,
		UpdatedAt:    now,
	}

	if err = dao.Db.Create(ftpTransfer).Error; err != nil {
		return nil, fmt.Errorf("创建FTP传输记录失败: %v", err)
	}

	// 异步执行FTP下载
	go func() {
		var conn *ftp.ServerConn
		var localFile *os.File
		var updateData = map[string]interface{}{
			"end_time":   gtime.Now(),
			"updated_at": gtime.Now(),
		}

		defer func() {
			dao.Db.Model(&modelFile.FtpTransfer{}).Where("transfer_id = ?", transferId).Updates(updateData)

			if localFile != nil {
				localFile.Close()
			}
			if conn != nil {
				conn.Quit()
			}
		}()

		conn, err = s.connectFTP(req.FtpServer, req.FtpPort, req.FtpUsername, req.FtpPassword)
		if err != nil {
			updateData["status"] = 2
			updateData["error_msg"] = err.Error()
			return
		}

		response, err := conn.Retr(req.FtpPath)
		if err != nil {
			updateData["status"] = 2
			updateData["error_msg"] = fmt.Sprintf("FTP下载失败: %v", err)
			return
		}
		defer response.Close()

		fileName := filepath.Base(req.FtpPath)
		relativePath, fileId := s.generateFilePath()
		localFilePath := filepath.Join(rootDir, relativePath)

		localFile, err = os.Create(localFilePath)
		if err != nil {
			updateData["status"] = 2
			updateData["error_msg"] = fmt.Sprintf("创建本地文件失败: %v", err)
			return
		}

		_, err = io.Copy(localFile, response)
		if err != nil {
			updateData["status"] = 2
			updateData["error_msg"] = fmt.Sprintf("保存文件失败: %v", err)
			return
		}

		fileInfo, err := localFile.Stat()
		if err != nil {
			updateData["status"] = 2
			updateData["error_msg"] = fmt.Sprintf("获取文件信息失败: %v", err)
			return
		}

		fileHash, err := s.calculateFileHash(localFilePath)
		if err != nil {
			updateData["status"] = 2
			updateData["error_msg"] = fmt.Sprintf("计算文件哈希值失败: %v", err)
			return
		}

		fileRecord := &modelFile.FileInfo{
			FileId:        fileId,
			FileName:      fileName,
			FilePath:      relativePath,
			FileSize:      fileInfo.Size(),
			FileType:      "application/octet-stream",
			FileExt:       filepath.Ext(fileName),
			FileHash:      fileHash,
			UploadType:    2,
			UploadUserId:  tools.GetUserIdFromCtx(ctx),
			DownloadCount: 0,
			IsPublic:      false,
			ExpireTime:    nil,
			CreatedAt:     gtime.Now(),
			UpdatedAt:     gtime.Now(),
		}

		if err = dao.Db.Create(fileRecord).Error; err != nil {
			updateData["status"] = 2
			updateData["error_msg"] = fmt.Sprintf("保存文件记录失败: %v", err)
			return
		}

		updateData["status"] = 1
		updateData["file_id"] = fileId
	}()

	res.TransferId = transferId
	res.Status = "进行中"

	return res, nil
}

// 12. 分片上传初始化
func (s *ServerFile) InitChunkUpload(ctx context.Context, req *file.InitChunkUploadReq) (res *file.InitChunkUploadRes, err error) {
	res = &file.InitChunkUploadRes{}

	fileId := uniqueId.GenerateID("file")
	now := gtime.Now()

	fileRecord := &modelFile.FileInfo{
		FileId:        fileId,
		FileName:      req.FileName,
		FilePath:      "",
		FileSize:      req.FileSize,
		FileType:      req.FileType,
		FileExt:       filepath.Ext(req.FileName),
		FileHash:      req.FileHash,
		UploadType:    3,
		UploadUserId:  tools.GetUserIdFromCtx(ctx),
		DownloadCount: 0,
		IsPublic:      req.IsPublic,
		ExpireTime:    nil,
		CreatedAt:     now,
		UpdatedAt:     now,
	}

	if err = dao.Db.Create(fileRecord).Error; err != nil {
		return nil, fmt.Errorf("创建文件记录失败: %v", err)
	}

	chunkCount := (req.FileSize + req.ChunkSize - 1) / req.ChunkSize

	for i := int64(0); i < chunkCount; i++ {
		chunkId := uniqueId.GenerateID("chunk")
		chunk := &modelFile.FileChunk{
			ChunkId:      chunkId,
			FileId:       fileId,
			ChunkIndex:   int(i),
			ChunkSize:    req.ChunkSize,
			ChunkPath:    "",
			ChunkHash:    "",
			IsUploaded:   false,
			UploadUserId: tools.GetUserIdFromCtx(ctx),
			CreatedAt:    now,
			UpdatedAt:    now,
		}

		if err = dao.Db.Create(chunk).Error; err != nil {
			return nil, fmt.Errorf("创建分片记录失败: %v", err)
		}
	}

	res.FileId = fileId
	res.ChunkCount = int(chunkCount)
	res.ChunkSize = req.ChunkSize

	return res, nil
}

// 13. 上传分片
func (s *ServerFile) UploadChunk(ctx context.Context, chunkFile *ghttp.UploadFile, req *file.UploadChunkReq) (res *file.UploadChunkRes, err error) {
	res = &file.UploadChunkRes{}

	var chunk modelFile.FileChunk
	result := dao.Db.Where("file_id = ? AND chunk_index = ?", req.FileId, req.ChunkIndex).First(&chunk)
	if result.Error != nil {
		return nil, errors.New("分片记录不存在")
	}

	if chunk.IsUploaded {
		res.Success = true
		res.Message = "分片已上传"
		return res, nil
	}

	chunkDir := filepath.Join(rootDir, "chunks", req.FileId)
	if !gfile.Exists(chunkDir) {
		err := gfile.Mkdir(chunkDir)
		if err != nil {
			return nil, fmt.Errorf("创建分片目录失败: %v", err)
		}
	}

	chunkPath := filepath.Join(chunkDir, strconv.Itoa(req.ChunkIndex))

	if _, err = chunkFile.Save(chunkPath); err != nil {
		return nil, fmt.Errorf("保存分片失败: %v", err)
	}

	chunkHash, err := s.calculateFileHash(chunkPath)
	if err != nil {
		return nil, fmt.Errorf("计算分片哈希值失败: %v", err)
	}

	if req.ChunkHash != "" && req.ChunkHash != chunkHash {
		os.Remove(chunkPath)
		return nil, errors.New("分片哈希值校验失败")
	}

	updateData := map[string]interface{}{
		"chunk_path":  chunkPath,
		"chunk_hash":  chunkHash,
		"is_uploaded": true,
		"updated_at":  gtime.Now(),
	}

	if err = dao.Db.Model(&chunk).Updates(updateData).Error; err != nil {
		return nil, fmt.Errorf("更新分片记录失败: %v", err)
	}

	res.Success = true
	res.Message = "分片上传成功"

	return res, nil
}

// 14. 完成分片上传
func (s *ServerFile) CompleteChunkUpload(ctx context.Context, req *file.CompleteChunkUploadReq) (res *file.CompleteChunkUploadRes, err error) {
	res = &file.CompleteChunkUploadRes{}

	var chunks []modelFile.FileChunk
	result := dao.Db.Where("file_id = ?", req.FileId).Order("chunk_index").Find(&chunks)
	if result.Error != nil {
		return nil, fmt.Errorf("查询分片记录失败: %v", result.Error)
	}

	for _, chunk := range chunks {
		if !chunk.IsUploaded {
			return nil, errors.New("存在未上传的分片")
		}
	}

	var fileInfo modelFile.FileInfo
	result = dao.Db.Where("file_id = ?", req.FileId).First(&fileInfo)
	if result.Error != nil {
		return nil, errors.New("文件记录不存在")
	}

	relativePath, _ := s.generateFilePath()
	absolutePath := filepath.Join(rootDir, relativePath)

	finalFile, err := os.Create(absolutePath)
	if err != nil {
		return nil, fmt.Errorf("创建最终文件失败: %v", err)
	}
	defer finalFile.Close()

	for _, chunk := range chunks {
		chunkFile, err := os.Open(chunk.ChunkPath)
		if err != nil {
			return nil, fmt.Errorf("打开分片文件失败: %v", err)
		}

		_, err = io.Copy(finalFile, chunkFile)
		chunkFile.Close()

		if err != nil {
			return nil, fmt.Errorf("合并分片失败: %v", err)
		}
	}

	finalFileHash, err := s.calculateFileHash(absolutePath)
	if err != nil {
		return nil, fmt.Errorf("计算最终文件哈希值失败: %v", err)
	}

	if req.FileHash != "" && req.FileHash != finalFileHash {
		os.Remove(absolutePath)
		return nil, errors.New("文件哈希值校验失败")
	}

	updateData := map[string]interface{}{
		"file_path":  relativePath,
		"file_hash":  finalFileHash,
		"updated_at": gtime.Now(),
	}

	if err = dao.Db.Model(&fileInfo).Updates(updateData).Error; err != nil {
		return nil, fmt.Errorf("更新文件记录失败: %v", err)
	}

	// 清理分片文件
	go func() {
		chunkDir := filepath.Join(rootDir, "chunks", req.FileId)
		if gfile.Exists(chunkDir) {
			os.RemoveAll(chunkDir)
		}

		dao.Db.Where("file_id = ?", req.FileId).Delete(&modelFile.FileChunk{})
	}()

	res.Success = true
	res.FileId = req.FileId
	res.FileName = fileInfo.FileName
	res.FileSize = fileInfo.FileSize
	res.FileUrl = "/file/download?file_id=" + req.FileId

	return res, nil
}

// 15. 检查文件上传状态(断点续传)
func (s *ServerFile) CheckUploadStatus(ctx context.Context, req *file.CheckUploadStatusReq) (res *file.CheckUploadStatusRes, err error) {
	res = &file.CheckUploadStatusRes{}

	// 1. 查询文件记录
	var fileInfo modelFile.FileInfo
	result := dao.Db.Where("file_hash = ? AND upload_type = ?", req.FileHash, req.UploadType).First(&fileInfo)

	if result.Error == nil {
		// 文件已存在
		res.Status = 2 // 已完成
		res.FileId = fileInfo.FileId
		res.UploadedSize = fileInfo.FileSize
		res.FileUrl = "/file/download?file_id=" + fileInfo.FileId
		return res, nil
	}

	// 2. 检查分片上传状态
	if req.UploadType == 3 {
		var chunks []modelFile.FileChunk
		dao.Db.Where("file_id = ?", req.FileId).Find(&chunks)

		var uploadedSize int64
		uploadedChunks := 0
		for _, chunk := range chunks {
			if chunk.IsUploaded {
				uploadedSize += chunk.ChunkSize
				uploadedChunks++
			}
		}

		res.Status = 1 // 部分上传
		res.UploadedSize = uploadedSize
		res.UploadedChunks = uploadedChunks
		res.TotalChunks = len(chunks)
		return res, nil
	}

	// 3. 检查HTTP断点续传
	if req.UploadType == 1 {
		tempFilePath := filepath.Join(s.getTempDir(), req.FileHash+".tmp")
		if gfile.Exists(tempFilePath) {
			fileInfo, err := os.Stat(tempFilePath)
			if err == nil {
				res.Status = 1 // 部分上传
				res.UploadedSize = fileInfo.Size()
				return res, nil
			}
		}
	}

	res.Status = 0 // 未开始
	return res, nil
}

// 16. HTTP断点续传上传
func (s *ServerFile) ResumeUploadFile(ctx context.Context, uploadFile *ghttp.UploadFile, req *file.ResumeUploadFileReq) (res *file.UploadFileRes, err error) {
	res = &file.UploadFileRes{}

	// 1. 验证参数
	if req.FileHash == "" {
		return nil, errors.New("文件哈希值不能为空")
	}

	// 2. 检查临时文件
	tempFilePath := filepath.Join(s.getTempDir(), req.FileHash+".tmp")
	var tempFile *os.File
	var currentSize int64

	if gfile.Exists(tempFilePath) {
		// 断点续传
		tempFileInfo, err := os.Stat(tempFilePath)
		if err != nil {
			return nil, fmt.Errorf("获取临时文件信息失败: %v", err)
		}
		currentSize = tempFileInfo.Size()

		// 验证起始位置
		if req.StartByte != currentSize {
			return nil, fmt.Errorf("起始位置不匹配，期望: %d，实际: %d", currentSize, req.StartByte)
		}

		tempFile, err = os.OpenFile(tempFilePath, os.O_WRONLY|os.O_APPEND, 0644)
		if err != nil {
			return nil, fmt.Errorf("打开临时文件失败: %v", err)
		}
	} else {
		// 新建文件
		if req.StartByte != 0 {
			return nil, errors.New("新文件起始位置必须为0")
		}

		tempFile, err = os.Create(tempFilePath)
		if err != nil {
			return nil, fmt.Errorf("创建临时文件失败: %v", err)
		}
	}
	defer tempFile.Close()

	// 3. 写入数据
	uploadFileReader, err := uploadFile.Open()
	if err != nil {
		return nil, fmt.Errorf("打开上传文件失败: %v", err)
	}
	defer uploadFileReader.Close()

	buffer := make([]byte, 32*1024) // 32KB缓冲区
	written, err := io.CopyBuffer(tempFile, uploadFileReader, buffer)
	if err != nil {
		return nil, fmt.Errorf("写入文件失败: %v", err)
	}

	currentSize += written

	// 4. 检查是否上传完成
	if currentSize >= req.TotalSize {
		// 验证文件哈希
		calculatedHash, err := s.calculateFileHash(tempFilePath)
		if err != nil {
			return nil, fmt.Errorf("计算文件哈希失败: %v", err)
		}

		if calculatedHash != req.FileHash {
			os.Remove(tempFilePath)
			return nil, errors.New("文件哈希验证失败")
		}

		// 移动到正式目录
		relativePath, fileId := s.generateFilePath()
		absolutePath := filepath.Join(rootDir, relativePath)

		err = os.Rename(tempFilePath, absolutePath)
		if err != nil {
			return nil, fmt.Errorf("移动文件失败: %v", err)
		}

		// 保存文件记录
		now := gtime.Now()

		fileRecord := &modelFile.FileInfo{
			FileId:        fileId,
			FileName:      req.FileName,
			FilePath:      relativePath,
			FileSize:      currentSize,
			FileType:      req.FileType,
			FileExt:       filepath.Ext(req.FileName),
			FileHash:      req.FileHash,
			UploadType:    1,
			UploadUserId:  tools.GetUserIdFromCtx(ctx),
			DownloadCount: 0,
			IsPublic:      req.IsPublic,
			ExpireTime:    nil,
			CreatedAt:     now,
			UpdatedAt:     now,
		}

		if err = dao.Db.Create(fileRecord).Error; err != nil {
			os.Remove(absolutePath)
			return nil, fmt.Errorf("保存文件记录失败: %v", err)
		}

		res.FileId = fileId
		res.FileName = req.FileName
		res.FileSize = currentSize
		res.FileType = req.FileType
		res.FileUrl = "/file/download?file_id=" + fileId
		res.IsComplete = true
	} else {
		res.IsComplete = false
		res.UploadedSize = currentSize
		res.NextByte = currentSize
	}

	return res, nil
}

// 17. FTP断点续传上传
func (s *ServerFile) ResumeFtpUpload(ctx context.Context, req *file.ResumeFtpUploadReq) (res *file.UploadFtpFileRes, err error) {
	res = &file.UploadFtpFileRes{}

	// 1. 获取本地文件信息
	fileInfo, err := s.GetFileInfo(ctx, req.FileId)
	if err != nil {
		return nil, err
	}

	localFilePath := filepath.Join(rootDir, fileInfo.FilePath)
	if !gfile.Exists(localFilePath) {
		return nil, errors.New("本地文件不存在")
	}

	// 2. 连接FTP服务器
	conn, err := s.connectFTP(req.FtpServer, req.FtpPort, req.FtpUsername, req.FtpPassword)
	if err != nil {
		return nil, err
	}
	defer conn.Quit()

	// 3. 检查远程文件大小
	var remoteSize int64
	if size, err := conn.FileSize(req.FtpPath); err == nil {
		remoteSize = size
	}

	localFileInfo, err := os.Stat(localFilePath)
	if err != nil {
		return nil, fmt.Errorf("获取本地文件信息失败: %v", err)
	}

	// 4. 如果远程文件已完整，直接返回成功
	if remoteSize >= localFileInfo.Size() {
		res.TransferId = uniqueId.GenerateID("ftp")
		res.Status = "已完成"
		return res, nil
	}

	// 5. 创建传输记录
	transferId := uniqueId.GenerateID("ftp")
	now := gtime.Now()

	ftpTransfer := &modelFile.FtpTransfer{
		TransferId:   transferId,
		FileId:       req.FileId,
		TransferType: 1,
		FtpServer:    req.FtpServer,
		FtpPort:      req.FtpPort,
		FtpPath:      req.FtpPath,
		Status:       0,
		UserId:       tools.GetUserIdFromCtx(ctx),
		StartTime:    now,
		CreatedAt:    now,
		UpdatedAt:    now,
	}

	if err = dao.Db.Create(ftpTransfer).Error; err != nil {
		return nil, fmt.Errorf("创建FTP传输记录失败: %v", err)
	}

	// 6. 异步断点续传
	go func() {
		var updateData = map[string]interface{}{
			"end_time":   gtime.Now(),
			"updated_at": gtime.Now(),
		}

		defer func() {
			dao.Db.Model(&modelFile.FtpTransfer{}).Where("transfer_id = ?", transferId).Updates(updateData)
		}()

		// 打开本地文件
		localFile, err := os.Open(localFilePath)
		if err != nil {
			updateData["status"] = 2
			updateData["error_msg"] = fmt.Sprintf("打开本地文件失败: %v", err)
			return
		}
		defer localFile.Close()

		// 定位到断点位置
		if remoteSize > 0 {
			_, err = localFile.Seek(remoteSize, 0)
			if err != nil {
				updateData["status"] = 2
				updateData["error_msg"] = fmt.Sprintf("定位文件位置失败: %v", err)
				return
			}
		}

		// 使用APPE命令追加上传
		if remoteSize > 0 {
			err = conn.Append(req.FtpPath, localFile)
		} else {
			err = conn.Stor(req.FtpPath, localFile)
		}

		if err != nil {
			updateData["status"] = 2
			updateData["error_msg"] = fmt.Sprintf("FTP上传失败: %v", err)
			return
		}

		updateData["status"] = 1
	}()

	res.TransferId = transferId

	res.Status = "进行中"

	return res, nil
}

// 18. 清理过期的临时文件
func (s *ServerFile) CleanExpiredTempFiles(ctx context.Context) error {
	tempDir := s.getTempDir()
	timeout := s.getResumeTimeout()

	return filepath.Walk(tempDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if !info.IsDir() && time.Since(info.ModTime()).Seconds() > float64(timeout) {
			g.Log().Info(ctx, "清理过期临时文件:", path)
			return os.Remove(path)
		}

		return nil
	})
}
