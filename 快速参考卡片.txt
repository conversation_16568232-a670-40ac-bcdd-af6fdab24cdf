═══════════════════════════════════════════════════════════════
                    Word表格2列4行布局 - 快速参考
═══════════════════════════════════════════════════════════════

📋 关键参数
─────────────────────────────────────────────────────────────
• 页边距：0.26cm (上下左右)
• 表格尺寸：10.09cm × 7.07cm
• 列间距：0.3cm
• 行间距：0.3cm
• 总布局：2列 × 4行 = 8个表格

🔧 核心步骤
─────────────────────────────────────────────────────────────
1. 设置页边距 → 布局 → 页边距 → 自定义 → 0.26cm
2. 插入表格 → 2列1行
3. 复制原表格到两个单元格
4. 设置表格属性 → 宽度10.09cm，高度7.07cm
5. 复制整行3次，形成4行
6. 调整行间距 → 段落 → 段后0.3cm

✂️ 裁剪方法
─────────────────────────────────────────────────────────────
1. 垂直对折
2. 水平裁剪3次
3. 每次得到2个表格
4. 总共8个相同表格

⚠️ 注意事项
─────────────────────────────────────────────────────────────
• 打印时选择100%缩放
• 使用A4纸
• 裁剪前检查对称性
• 保持原表格格式不变

═══════════════════════════════════════════════════════════════
