/*
******		FileName	:	file.go
******		Describe	:	此文件主要用于文件管理相关的API接口定义
******		Date		:	2025-05-08
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:   文件管理相关的请求和响应结构体定义
 */

package file

import "github.com/gogf/gf/v2/frame/g"

// 1. HTTP文件上传请求
type UploadFileReq struct {
	g.Meta `path:"/http/upload" tags:"文件管理" method:"post" summary:"1、HTTP文件上传"`

	FileType string `p:"file_type" dc:"文件类型(MIME类型)"`
	IsPublic bool   `p:"is_public" default:"false" dc:"是否公开访问"`
}

// 1. HTTP文件上传响应
type UploadFileRes struct {
	g.Meta `mime:"application/json" example:"string"`

	FileId       string `json:"file_id" dc:"文件唯一ID"`
	FileName     string `json:"file_name" dc:"文件名称"`
	FileSize     int64  `json:"file_size" dc:"文件大小(字节)"`
	FileType     string `json:"file_type" dc:"文件类型"`
	FileUrl      string `json:"file_url" dc:"文件下载URL"`
	IsExist      bool   `json:"is_exist" dc:"文件是否已存在(去重)"`
	IsComplete   bool   `json:"is_complete" dc:"文件是否已完成"`
	UploadedSize int64  `json:"uploaded_size" dc:"已上传大小"`
	NextByte     int64  `json:"next_byte" dc:"下个数据大小"`
}

// 2. HTTP文件下载请求
type DownloadFileReq struct {
	g.Meta `path:"/http/download" tags:"文件管理" method:"get" summary:"2、HTTP文件下载"`

	FileId string `v:"required|length:1,64#请输入文件ID|文件ID长度不能超过64个字符" p:"file_id" dc:"文件唯一ID"`
}

// 2. HTTP文件下载响应
type DownloadFileRes struct {
	g.Meta `mime:"application/octet-stream"`
}

// 3. 获取文件列表请求
type GetFileListReq struct {
	g.Meta `path:"/list" tags:"文件管理" method:"get" summary:"3、获取文件列表"`

	FileName     string `p:"file_name" dc:"文件名称(模糊查询)"`
	FileType     string `p:"file_type" dc:"文件类型"`
	UploadType   int    `p:"upload_type" default:"0"  dc:"上传类型(1:HTTP,2:FTP,3:分片), 不传默认全部"`
	UploadUserId string `p:"upload_user_id" dc:"上传用户ID"`
	Page         int    `p:"page" default:"1" dc:"页码"`
	Size         int    `p:"size" default:"20" dc:"每页数量"`
	OrderBy      string `p:"order_by" default:"created_at DESC" dc:"排序方式"`
}

// 3. 获取文件列表响应
type GetFileListRes struct {
	g.Meta `mime:"application/json" example:"string"`

	Total int64           `json:"total" dc:"总数量"`
	List  []*FileInfoItem `json:"list" dc:"文件列表"`
}

// 文件信息项
type FileInfoItem struct {
	FileId        string `json:"file_id" dc:"文件唯一ID"`
	FileName      string `json:"file_name" dc:"文件名称"`
	FileSize      int64  `json:"file_size" dc:"文件大小(字节)"`
	FileType      string `json:"file_type" dc:"文件类型"`
	FileExt       string `json:"file_ext" dc:"文件扩展名"`
	UploadType    int    `json:"upload_type" dc:"上传类型(1:HTTP,2:FTP,3:分片)"`
	UploadUserId  string `json:"upload_user_id" dc:"上传用户ID"`
	DownloadCount int    `json:"download_count" dc:"下载次数"`
	IsPublic      bool   `json:"is_public" dc:"是否公开访问"`
	UploadTime    string `json:"upload_time" dc:"上传时间"`
	ExpireTime    string `json:"expire_time" dc:"过期时间"`
}

// 4. 删除文件请求
type DeleteFileReq struct {
	g.Meta `path:"/delete" tags:"文件管理" method:"get" summary:"4、删除文件"`

	FileId string `v:"required|length:1,64#请输入文件ID|文件ID长度不能超过64个字符" p:"file_id" dc:"文件唯一ID"`
}

// 4. 删除文件响应
type DeleteFileRes struct {
	g.Meta `mime:"application/json" example:"string"`

	Success bool `json:"success" dc:"删除是否成功"`
}

// 5. FTP文件上传请求
type UploadFtpFileReq struct {
	g.Meta `path:"/ftp/upload" tags:"文件管理" method:"post" summary:"5、FTP文件上传"`

	FileId      string `v:"required|length:1,64#请输入文件ID|文件ID长度不能超过64个字符" p:"file_id" dc:"要上传的文件ID"`
	FtpServer   string `v:"required|length:1,255#请输入FTP服务器地址|FTP服务器地址长度不能超过255个字符" p:"ftp_server" dc:"FTP服务器地址"`
	FtpPort     int    `v:"required|between:1,65535#请输入FTP端口|FTP端口范围1-65535" p:"ftp_port" dc:"FTP服务器端口"`
	FtpUsername string `v:"required|length:1,100#请输入FTP用户名|FTP用户名长度不能超过100个字符" p:"ftp_username" dc:"FTP用户名"`
	FtpPassword string `v:"required|length:1,100#请输入FTP密码|FTP密码长度不能超过100个字符" p:"ftp_password" dc:"FTP密码"`
	FtpPath     string `v:"required|length:1,512#请输入FTP文件路径|FTP文件路径长度不能超过512个字符" p:"ftp_path" dc:"FTP文件路径"`
}

// 5. FTP文件上传响应
type UploadFtpFileRes struct {
	g.Meta `mime:"application/json" example:"string"`

	TransferId string `json:"transfer_id" dc:"传输记录ID"`
	Status     string `json:"status" dc:"传输状态"`
}

// 6. FTP文件下载请求
type DownloadFtpFileReq struct {
	g.Meta `path:"/ftp/download" tags:"文件管理" method:"post" summary:"6、FTP文件下载"`

	FtpServer   string `v:"required|length:1,255#请输入FTP服务器地址|FTP服务器地址长度不能超过255个字符" p:"ftp_server" dc:"FTP服务器地址"`
	FtpPort     int    `v:"required|between:1,65535#请输入FTP端口|FTP端口范围1-65535" p:"ftp_port" dc:"FTP服务器端口"`
	FtpUsername string `v:"required|length:1,100#请输入FTP用户名|FTP用户名长度不能超过100个字符" p:"ftp_username" dc:"FTP用户名"`
	FtpPassword string `v:"required|length:1,100#请输入FTP密码|FTP密码长度不能超过100个字符" p:"ftp_password" dc:"FTP密码"`
	FtpPath     string `v:"required|length:1,512#请输入FTP文件路径|FTP文件路径长度不能超过512个字符" p:"ftp_path" dc:"FTP文件路径"`
}

// 6. FTP文件下载响应
type DownloadFtpFileRes struct {
	g.Meta `mime:"application/json" example:"string"`

	TransferId string `json:"transfer_id" dc:"传输记录ID"`
	Status     string `json:"status" dc:"传输状态"`
}

// 7. 分片上传初始化请求
type InitChunkUploadReq struct {
	g.Meta `path:"/chunk/init" tags:"文件管理" method:"post" summary:"7、分片上传初始化"`

	FileName  string `v:"required|length:1,255#请输入文件名|文件名长度不能超过255个字符" p:"file_name" dc:"文件名称"`
	FileSize  int64  `v:"required|min:1#请输入文件大小|文件大小必须大于0" p:"file_size" dc:"文件总大小(字节)"`
	FileType  string `p:"file_type" dc:"文件类型(MIME类型)"`
	FileHash  string `p:"file_hash" dc:"文件哈希值(SHA256)"`
	ChunkSize int64  `v:"required|min:1024#请输入分片大小|分片大小不能小于1024字节" p:"chunk_size" dc:"分片大小(字节)"`
	IsPublic  bool   `p:"is_public" default:"false" dc:"是否公开访问"`
}

// 7. 分片上传初始化响应
type InitChunkUploadRes struct {
	g.Meta `mime:"application/json" example:"string"`

	FileId     string `json:"file_id" dc:"文件唯一ID"`
	ChunkCount int    `json:"chunk_count" dc:"分片总数"`
	ChunkSize  int64  `json:"chunk_size" dc:"分片大小"`
}

// 8. 上传分片请求
type UploadChunkReq struct {
	g.Meta `path:"/chunk/upload" tags:"文件管理" method:"post" summary:"8、上传分片"`

	FileId     string `v:"required|length:1,64#请输入文件ID|文件ID长度不能超过64个字符" p:"file_id" dc:"文件唯一ID"`
	ChunkIndex int    `v:"required|min:0#请输入分片索引|分片索引不能小于0" p:"chunk_index" dc:"分片索引号"`
	ChunkHash  string `p:"chunk_hash" dc:"分片哈希值(用于校验)"`
}

// 8. 上传分片响应
type UploadChunkRes struct {
	g.Meta `mime:"application/json" example:"string"`

	Success bool   `json:"success" dc:"上传是否成功"`
	Message string `json:"message" dc:"提示消息"`
}

// 9. 完成分片上传请求
type CompleteChunkUploadReq struct {
	g.Meta `path:"/chunk/complete" tags:"文件管理" method:"post" summary:"9、完成分片上传"`

	FileId   string `v:"required|length:1,64#请输入文件ID|文件ID长度不能超过64个字符" p:"file_id" dc:"文件唯一ID"`
	FileHash string `p:"file_hash" dc:"文件哈希值(用于最终校验)"`
}

// 9. 完成分片上传响应
type CompleteChunkUploadRes struct {
	g.Meta `mime:"application/json" example:"string"`

	Success  bool   `json:"success" dc:"合并是否成功"`
	FileId   string `json:"file_id" dc:"文件唯一ID"`
	FileName string `json:"file_name" dc:"文件名称"`
	FileSize int64  `json:"file_size" dc:"文件大小"`
	FileUrl  string `json:"file_url" dc:"文件下载URL"`
}

// 10. 获取传输状态请求
type GetTransferStatusReq struct {
	g.Meta `path:"/transfer/status" tags:"文件管理" method:"get" summary:"10、获取传输状态"`

	TransferId string `v:"required|length:1,64#请输入传输ID|传输ID长度不能超过64个字符" p:"transfer_id" dc:"传输记录ID"`
}

// 10. 获取传输状态响应
type GetTransferStatusRes struct {
	g.Meta `mime:"application/json" example:"string"`

	TransferId   string `json:"transfer_id" dc:"传输记录ID"`
	FileId       string `json:"file_id" dc:"关联的文件ID"`
	TransferType int    `json:"transfer_type" dc:"传输类型(1:上传,2:下载)"`
	Status       int    `json:"status" dc:"状态(0:进行中,1:成功,2:失败)"`
	ErrorMsg     string `json:"error_msg" dc:"错误信息"`
	StartTime    string `json:"start_time" dc:"开始时间"`
	EndTime      string `json:"end_time" dc:"结束时间"`
}

// 11. 设置文件访问权限请求
type SetFileAccessReq struct {
	g.Meta `path:"/access/set" tags:"文件管理" method:"post" summary:"11、设置文件访问权限"`

	FileId     string `v:"required|length:1,64#请输入文件ID|文件ID长度不能超过64个字符" p:"file_id" dc:"文件唯一ID"`
	UserId     string `v:"required|length:1,32#请输入用户ID|用户ID长度不能超过32个字符" p:"user_id" dc:"用户ID"`
	AccessType int    `v:"required|in:1,2,3#请选择访问类型|访问类型只能是1,2,3" p:"access_type" dc:"访问类型(1:读,2:写,3:读写)"`
	ExpireTime string `p:"expire_time" dc:"过期时间(格式:2006-01-02 15:04:05)"`
}

// 11. 设置文件访问权限响应
type SetFileAccessRes struct {
	g.Meta `mime:"application/json" example:"string"`

	AccessId string `json:"access_id" dc:"访问权限ID"`
	Success  bool   `json:"success" dc:"设置是否成功"`
}

// 12. 获取文件访问权限请求
type GetFileAccessReq struct {
	g.Meta `path:"/access/list" tags:"文件管理" method:"get" summary:"12、获取文件访问权限"`

	FileId string `v:"required|length:1,64#请输入文件ID|文件ID长度不能超过64个字符" p:"file_id" dc:"文件唯一ID"`
}

// 12. 获取文件访问权限响应
type GetFileAccessRes struct {
	g.Meta `mime:"application/json" example:"string"`

	List []*FileAccessItem `json:"list" dc:"访问权限列表"`
}

// 文件访问权限项
type FileAccessItem struct {
	AccessId   string `json:"access_id" dc:"访问权限ID"`
	UserId     string `json:"user_id" dc:"用户ID"`
	AccessType int    `json:"access_type" dc:"访问类型(1:读,2:写,3:读写)"`
	ExpireTime string `json:"expire_time" dc:"过期时间"`
	CreatedAt  string `json:"created_at" dc:"创建时间"`
}

// 13. 删除文件访问权限请求
type DeleteFileAccessReq struct {
	g.Meta `path:"/access/delete" tags:"文件管理" method:"delete" summary:"13、删除文件访问权限"`

	AccessId string `v:"required|length:1,64#请输入访问权限ID|访问权限ID长度不能超过64个字符" p:"access_id" dc:"访问权限ID"`
}

// 13. 删除文件访问权限响应
type DeleteFileAccessRes struct {
	g.Meta `mime:"application/json" example:"string"`

	Success bool `json:"success" dc:"删除是否成功"`
}

// 14. 获取文件统计信息请求
type GetFileStatsReq struct {
	g.Meta `path:"/stats" tags:"文件管理" method:"get" summary:"14、获取文件统计信息"`

	UserId string `p:"user_id" dc:"用户ID(为空则统计所有用户)"`
}

// 14. 获取文件统计信息响应
type GetFileStatsRes struct {
	g.Meta `mime:"application/json" example:"string"`

	TotalFiles     int64 `json:"total_files" dc:"文件总数"`
	TotalSize      int64 `json:"total_size" dc:"文件总大小(字节)"`
	HttpFiles      int64 `json:"http_files" dc:"HTTP上传文件数"`
	FtpFiles       int64 `json:"ftp_files" dc:"FTP上传文件数"`
	ChunkFiles     int64 `json:"chunk_files" dc:"分片上传文件数"`
	PublicFiles    int64 `json:"public_files" dc:"公开文件数"`
	PrivateFiles   int64 `json:"private_files" dc:"私有文件数"`
	TotalDownloads int64 `json:"total_downloads" dc:"总下载次数"`
}

// 15. 检查文件上传状态请求
type CheckUploadStatusReq struct {
	g.Meta `path:"/upload/status" tags:"文件管理" method:"get" summary:"15、检查文件上传状态"`

	FileHash   string `v:"required|length:1,64#请输入文件哈希值|文件哈希值长度不能超过64个字符" p:"file_hash" dc:"文件哈希值(SHA256)"`
	UploadType int    `v:"required|in:1,2,3#请选择上传类型|上传类型只能是1,2,3" p:"upload_type" dc:"上传类型(1:HTTP,2:FTP,3:分片)"`
	FileId     string `p:"file_id" dc:"文件ID(分片上传时必填)"`
}

// 15. 检查文件上传状态响应
type CheckUploadStatusRes struct {
	g.Meta `mime:"application/json" example:"string"`

	Status         int    `json:"status" dc:"上传状态(0:未开始,1:部分上传,2:已完成)"`
	FileId         string `json:"file_id" dc:"文件唯一ID"`
	UploadedSize   int64  `json:"uploaded_size" dc:"已上传大小(字节)"`
	UploadedChunks int    `json:"uploaded_chunks" dc:"已上传分片数"`
	TotalChunks    int    `json:"total_chunks" dc:"总分片数"`
	FileUrl        string `json:"file_url" dc:"文件下载URL(完成时)"`
}

// 16. HTTP断点续传上传请求
type ResumeUploadFileReq struct {
	g.Meta `path:"/upload/resume" tags:"文件管理" method:"post" summary:"16、HTTP断点续传上传"`

	FileName  string `v:"required|length:1,255#请输入文件名|文件名长度不能超过255个字符" p:"file_name" dc:"文件名称"`
	FileHash  string `v:"required|length:1,64#请输入文件哈希值|文件哈希值长度不能超过64个字符" p:"file_hash" dc:"文件哈希值(SHA256)"`
	FileType  string `p:"file_type" dc:"文件类型(MIME类型)"`
	TotalSize int64  `v:"required|min:1#请输入文件总大小|文件大小必须大于0" p:"total_size" dc:"文件总大小(字节)"`
	StartByte int64  `v:"min:0#起始字节不能小于0" p:"start_byte" dc:"起始字节位置"`
	IsPublic  bool   `p:"is_public" default:"false" dc:"是否公开访问"`
}

// 16. HTTP断点续传上传响应
type ResumeUploadFileRes struct {
	g.Meta `mime:"application/json" example:"string"`

	FileId       string `json:"file_id" dc:"文件唯一ID"`
	FileName     string `json:"file_name" dc:"文件名称"`
	FileSize     int64  `json:"file_size" dc:"文件大小"`
	FileType     string `json:"file_type" dc:"文件类型"`
	FileUrl      string `json:"file_url" dc:"文件下载URL"`
	IsComplete   bool   `json:"is_complete" dc:"是否上传完成"`
	UploadedSize int64  `json:"uploaded_size" dc:"已上传大小(字节)"`
	NextByte     int64  `json:"next_byte" dc:"下次上传起始位置"`
}

// 17. FTP断点续传上传请求
type ResumeFtpUploadReq struct {
	g.Meta `path:"/ftp/upload/resume" tags:"文件管理" method:"post" summary:"17、FTP断点续传上传"`

	FileId      string `v:"required|length:1,64#请输入文件ID|文件ID长度不能超过64个字符" p:"file_id" dc:"要上传的文件ID"`
	FtpServer   string `v:"required|length:1,255#请输入FTP服务器地址|FTP服务器地址长度不能超过255个字符" p:"ftp_server" dc:"FTP服务器地址"`
	FtpPort     int    `v:"required|between:1,65535#请输入FTP端口|FTP端口范围1-65535" p:"ftp_port" dc:"FTP服务器端口"`
	FtpUsername string `v:"required|length:1,100#请输入FTP用户名|FTP用户名长度不能超过100个字符" p:"ftp_username" dc:"FTP用户名"`
	FtpPassword string `v:"required|length:1,100#请输入FTP密码|FTP密码长度不能超过100个字符" p:"ftp_password" dc:"FTP密码"`
	FtpPath     string `v:"required|length:1,512#请输入FTP文件路径|FTP文件路径长度不能超过512个字符" p:"ftp_path" dc:"FTP文件路径"`
}

// 17. FTP断点续传上传响应
type ResumeFtpUploadRes struct {
	g.Meta `mime:"application/json" example:"string"`

	TransferId string `json:"transfer_id" dc:"传输记录ID"`
	Status     string `json:"status" dc:"传输状态"`
}

// 18. 清理过期临时文件请求
type CleanExpiredTempFilesReq struct {
	g.Meta `path:"/temp/clean" tags:"文件管理" method:"post" summary:"18、清理过期临时文件"`
}

// 18. 清理过期临时文件响应
type CleanExpiredTempFilesRes struct {
	g.Meta `mime:"application/json" example:"string"`

	Success      bool `json:"success" dc:"清理是否成功"`
	CleanedCount int  `json:"cleaned_count" dc:"清理的文件数量"`
}

// 19. 获取文件详细信息请求
type GetFileDetailReq struct {
	g.Meta `path:"/detail" tags:"文件管理" method:"get" summary:"19、获取文件详细信息"`

	FileId string `v:"required|length:1,64#请输入文件ID|文件ID长度不能超过64个字符" p:"file_id" dc:"文件唯一ID"`
}

// 19. 获取文件详细信息响应
type GetFileDetailRes struct {
	g.Meta `mime:"application/json" example:"string"`

	FileId        string `json:"file_id" dc:"文件唯一ID"`
	FileName      string `json:"file_name" dc:"文件名称"`
	FilePath      string `json:"file_path" dc:"文件存储路径"`
	FileSize      int64  `json:"file_size" dc:"文件大小(字节)"`
	FileType      string `json:"file_type" dc:"文件类型"`
	FileExt       string `json:"file_ext" dc:"文件扩展名"`
	FileHash      string `json:"file_hash" dc:"文件哈希值"`
	UploadType    int    `json:"upload_type" dc:"上传类型(1:HTTP,2:FTP,3:分片)"`
	UploadUserId  string `json:"upload_user_id" dc:"上传用户ID"`
	DownloadCount int    `json:"download_count" dc:"下载次数"`
	IsPublic      bool   `json:"is_public" dc:"是否公开访问"`
	ExpireTime    string `json:"expire_time" dc:"过期时间"`
	CreatedAt     string `json:"created_at" dc:"创建时间"`
	UpdatedAt     string `json:"updated_at" dc:"更新时间"`
}

// 20. 批量删除文件请求
type BatchDeleteFileReq struct {
	g.Meta `path:"/batch/delete" tags:"文件管理" method:"delete" summary:"20、批量删除文件"`

	FileIds []string `v:"required|length:1,100#请输入文件ID列表|文件ID列表长度不能超过100个" p:"file_ids" dc:"文件ID列表"`
}

// 20. 批量删除文件响应
type BatchDeleteFileRes struct {
	g.Meta `mime:"application/json" example:"string"`

	SuccessCount int      `json:"success_count" dc:"成功删除数量"`
	FailedCount  int      `json:"failed_count" dc:"删除失败数量"`
	FailedFiles  []string `json:"failed_files" dc:"删除失败的文件ID列表"`
}

// 21. 文件重命名请求
type RenameFileReq struct {
	g.Meta `path:"/rename" tags:"文件管理" method:"put" summary:"21、文件重命名"`

	FileId  string `v:"required|length:1,64#请输入文件ID|文件ID长度不能超过64个字符" p:"file_id" dc:"文件唯一ID"`
	NewName string `v:"required|length:1,255#请输入新文件名|文件名长度不能超过255个字符" p:"new_name" dc:"新文件名"`
}

// 21. 文件重命名响应
type RenameFileRes struct {
	g.Meta `mime:"application/json" example:"string"`

	Success bool `json:"success" dc:"重命名是否成功"`
}

// 22. 文件复制请求
type CopyFileReq struct {
	g.Meta `path:"/copy" tags:"文件管理" method:"post" summary:"22、文件复制"`

	FileId   string `v:"required|length:1,64#请输入文件ID|文件ID长度不能超过64个字符" p:"file_id" dc:"源文件ID"`
	NewName  string `p:"new_name" dc:"新文件名(可选，默认为原文件名_copy)"`
	IsPublic bool   `p:"is_public" dc:"是否公开访问"`
}

// 22. 文件复制响应
type CopyFileRes struct {
	g.Meta `mime:"application/json" example:"string"`

	NewFileId string `json:"new_file_id" dc:"新文件ID"`
	FileName  string `json:"file_name" dc:"新文件名"`
	FileUrl   string `json:"file_url" dc:"新文件下载URL"`
}

// 23. 文件移动请求
type MoveFileReq struct {
	g.Meta `path:"/move" tags:"文件管理" method:"put" summary:"23、文件移动"`

	FileId     string `v:"required|length:1,64#请输入文件ID|文件ID长度不能超过64个字符" p:"file_id" dc:"文件唯一ID"`
	TargetPath string `v:"required|length:1,512#请输入目标路径|目标路径长度不能超过512个字符" p:"target_path" dc:"目标路径"`
}

// 23. 文件移动响应
type MoveFileRes struct {
	g.Meta `mime:"application/json" example:"string"`

	Success bool `json:"success" dc:"移动是否成功"`
}

// 24. 获取上传进度请求
type GetUploadProgressReq struct {
	g.Meta `path:"/upload/progress" tags:"文件管理" method:"get" summary:"24、获取上传进度"`

	FileHash string `v:"required|length:1,64#请输入文件哈希值|文件哈希值长度不能超过64个字符" p:"file_hash" dc:"文件哈希值"`
	FileId   string `p:"file_id" dc:"文件ID(分片上传时使用)"`
}

// 24. 获取上传进度响应
type GetUploadProgressRes struct {
	g.Meta `mime:"application/json" example:"string"`

	FileHash       string  `json:"file_hash" dc:"文件哈希值"`
	TotalSize      int64   `json:"total_size" dc:"文件总大小"`
	UploadedSize   int64   `json:"uploaded_size" dc:"已上传大小"`
	Progress       float64 `json:"progress" dc:"上传进度(百分比)"`
	UploadedChunks int     `json:"uploaded_chunks" dc:"已上传分片数"`
	TotalChunks    int     `json:"total_chunks" dc:"总分片数"`
	Status         int     `json:"status" dc:"上传状态(0:未开始,1:进行中,2:已完成,3:失败)"`
}

// 25. 设置文件过期时间请求
type SetFileExpireReq struct {
	g.Meta `path:"/expire/set" tags:"文件管理" method:"put" summary:"25、设置文件过期时间"`

	FileId     string `v:"required|length:1,64#请输入文件ID|文件ID长度不能超过64个字符" p:"file_id" dc:"文件唯一ID"`
	ExpireTime string `p:"expire_time" dc:"过期时间(格式:2006-01-02 15:04:05，为空表示永不过期)"`
}

// 25. 设置文件过期时间响应
type SetFileExpireRes struct {
	g.Meta `mime:"application/json" example:"string"`

	Success bool `json:"success" dc:"设置是否成功"`
}

// 26. 获取文件缩略图请求
type GetFileThumbnailReq struct {
	g.Meta `path:"/thumbnail" tags:"文件管理" method:"get" summary:"26、获取文件缩略图"`

	FileId string `v:"required|length:1,64#请输入文件ID|文件ID长度不能超过64个字符" p:"file_id" dc:"文件唯一ID"`
	Width  int    `p:"width" default:"200" dc:"缩略图宽度"`
	Height int    `p:"height" default:"200" dc:"缩略图高度"`
}

// 26. 获取文件缩略图响应
type GetFileThumbnailRes struct {
	g.Meta `mime:"image/jpeg"`
}

// 27. 文件预览请求
type PreviewFileReq struct {
	g.Meta `path:"/preview" tags:"文件管理" method:"get" summary:"27、文件预览"`

	FileId string `v:"required|length:1,64#请输入文件ID|文件ID长度不能超过64个字符" p:"file_id" dc:"文件唯一ID"`
}

// 27. 文件预览响应
type PreviewFileRes struct {
	g.Meta `mime:"application/octet-stream"`
}

// 28. 获取文件分享链接请求
type GetShareLinkReq struct {
	g.Meta `path:"/share/create" tags:"文件管理" method:"post" summary:"28、获取文件分享链接"`

	FileId      string `v:"required|length:1,64#请输入文件ID|文件ID长度不能超过64个字符" p:"file_id" dc:"文件唯一ID"`
	ExpireTime  string `p:"expire_time" dc:"分享过期时间(格式:2006-01-02 15:04:05)"`
	Password    string `p:"password" dc:"分享密码(可选)"`
	MaxDownload int    `p:"max_download" default:"0" dc:"最大下载次数(0表示不限制)"`
}

// 28. 获取文件分享链接响应
type GetShareLinkRes struct {
	g.Meta `mime:"application/json" example:"string"`

	ShareId    string `json:"share_id" dc:"分享ID"`
	ShareUrl   string `json:"share_url" dc:"分享链接"`
	Password   string `json:"password" dc:"分享密码"`
	ExpireTime string `json:"expire_time" dc:"过期时间"`
}

// 29. 通过分享链接下载文件请求
type DownloadByShareReq struct {
	g.Meta `path:"/share/download" tags:"文件管理" method:"get" summary:"29、通过分享链接下载文件"`

	ShareId  string `v:"required|length:1,64#请输入分享ID|分享ID长度不能超过64个字符" p:"share_id" dc:"分享ID"`
	Password string `p:"password" dc:"分享密码"`
}

// 29. 通过分享链接下载文件响应
type DownloadByShareRes struct {
	g.Meta `mime:"application/octet-stream"`
}

// 30. 取消文件分享请求
type CancelShareReq struct {
	g.Meta `path:"/share/cancel" tags:"文件管理" method:"delete" summary:"30、取消文件分享"`

	ShareId string `v:"required|length:1,64#请输入分享ID|分享ID长度不能超过64个字符" p:"share_id" dc:"分享ID"`
}

// 30. 取消文件分享响应
type CancelShareRes struct {
	g.Meta `mime:"application/json" example:"string"`

	Success bool `json:"success" dc:"取消是否成功"`
}
