/*
******        FileName    :   file.go
******        Describe    :   此文件主要用于文件管理服务器
******        Date        :   2025-04-03
******        Author      :   TangJinFei
******        Copyright   :   Guangzhou AiYunJi Inc.
******        Note        :   文件服务相关的控制器实现
 */

package file

import (
	"ayj_file_back/api/file"
	"ayj_file_back/internal/public/response"
	server "ayj_file_back/internal/service/file"
	"context"
	"strconv"

	"github.com/gogf/gf/v2/frame/g"
)

var FileApi = CtrlFile{}

type CtrlFile struct {
	server *server.ServerFile
}

// UploadFile HTTP文件上传
func (c *CtrlFile) UploadFile(ctx context.Context, req *file.UploadFileReq) (res *file.UploadFileRes, err error) {
	// 1. 获取上传的文件
	uploadFile := g.RequestFromCtx(ctx).GetUploadFile("file")
	if uploadFile == nil {
		response.Error(ctx, "未找到上传文件")
		return
	}

	// 2. 调用服务层处理文件上传
	res, err = c.server.UploadFile(ctx, uploadFile, req)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// DownloadFile HTTP文件下载
func (c *CtrlFile) DownloadFile(ctx context.Context, req *file.DownloadFileReq) (res *file.DownloadFileRes, err error) {
	// 1. 调用服务层获取文件信息
	fileInfo, err := c.server.GetFileInfo(ctx, req.FileId)
	if err != nil {
		response.Error(ctx, err.Error())
		return
	}

	// 2. 设置响应头
	r := g.RequestFromCtx(ctx)
	r.Response.Header().Set("Content-Type", "application/octet-stream")
	r.Response.Header().Set("Content-Disposition", "attachment; filename="+fileInfo.FileName)
	r.Response.Header().Set("Content-Length", strconv.FormatInt(fileInfo.FileSize, 10))

	// 3. 调用服务层处理文件下载
	err = c.server.DownloadFile(ctx, fileInfo.FilePath, r.Response.Writer)
	if err != nil {
		response.Error(ctx, err.Error())
	}
	return
}

// GetFileList 获取文件列表
func (c *CtrlFile) GetFileList(ctx context.Context, req *file.GetFileListReq) (res *file.GetFileListRes, err error) {
	res, err = c.server.GetFileList(ctx, req)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// DeleteFile 删除文件
func (c *CtrlFile) DeleteFile(ctx context.Context, req *file.DeleteFileReq) (res *file.DeleteFileRes, err error) {
	res, err = c.server.DeleteFile(ctx, req.FileId)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// UploadFtpFile FTP文件上传
func (c *CtrlFile) UploadFtpFile(ctx context.Context, req *file.UploadFtpFileReq) (res *file.UploadFtpFileRes, err error) {
	res, err = c.server.UploadFtpFile(ctx, req)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// DownloadFtpFile FTP文件下载
func (c *CtrlFile) DownloadFtpFile(ctx context.Context, req *file.DownloadFtpFileReq) (res *file.DownloadFtpFileRes, err error) {
	res, err = c.server.DownloadFtpFile(ctx, req)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// InitChunkUpload 分片上传初始化
func (c *CtrlFile) InitChunkUpload(ctx context.Context, req *file.InitChunkUploadReq) (res *file.InitChunkUploadRes, err error) {
	res, err = c.server.InitChunkUpload(ctx, req)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// UploadChunk 上传分片
func (c *CtrlFile) UploadChunk(ctx context.Context, req *file.UploadChunkReq) (res *file.UploadChunkRes, err error) {
	// 1. 获取上传的分片文件
	chunkFile := g.RequestFromCtx(ctx).GetUploadFile("chunk")
	if chunkFile == nil {
		response.Error(ctx, "未找到上传分片")
		return
	}

	// 2. 调用服务层处理分片上传
	res, err = c.server.UploadChunk(ctx, chunkFile, req)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}

// CompleteChunkUpload 完成分片上传
func (c *CtrlFile) CompleteChunkUpload(ctx context.Context, req *file.CompleteChunkUploadReq) (res *file.CompleteChunkUploadRes, err error) {
	res, err = c.server.CompleteChunkUpload(ctx, req)
	if err != nil {
		response.Error(ctx, err.Error())
	} else {
		response.Success(ctx, res)
	}
	return
}
