/*
******		FileName	:	file.go
******		Describe	:	此文件主要用于文件管理， http与 ftp文件管理服务器
******		Date		:	2025-05-08
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:   文件管理的数据库模型
 */

package modelFile

import (
	"github.com/gogf/gf/v2/os/gtime"
	"gorm.io/gorm"
)

// FileInfo 文件信息表
type FileInfo struct {
	gorm.Model

	FileId          string `gorm:"column:file_id;size:64;index;unique;comment:文件唯一ID" json:"file_id"`
	FileName        string `gorm:"column:file_name;size:255;comment:第一次上传的文件名称" json:"file_name"`
	FilePath        string `gorm:"column:file_path;size:512;comment:文件存储路径" json:"file_path"`
	FileSize        int64  `gorm:"column:file_size;comment:文件大小(字节)" json:"file_size"`
	FileType        string `gorm:"column:file_type;size:50;comment:文件类型(MIME类型)" json:"file_type"`
	FileExt         string `gorm:"column:file_ext;size:20;comment:文件扩展名" json:"file_ext"`
	FileHash        string `gorm:"column:file_hash;size:64;index;comment:文件内容哈希值(SHA256)" json:"file_hash"`
	UploadType      int    `gorm:"column:upload_type;comment:上传类型(1:HTTP,2:FTP,3:分片)" json:"upload_type"`
	UploadUserId    string `gorm:"column:upload_user_id;size:32;comment:上传用户ID" json:"upload_user_id"`
	DownloadCount   int    `gorm:"column:download_count;default:0;comment:下载次数" json:"download_count"`
	UploadUserCount int    `gorm:"column:upload_user_count;default:1;comment:同一文件用户上传数量" json:"upload_user_count"`
	IsPublic        bool   `gorm:"column:is_public;default:false;comment:是否公开访问" json:"is_public"`

	ExpireTime *gtime.Time `gorm:"column:expire_time;comment:过期时间,null表示永不过期" json:"expire_time"`
	CreatedAt  *gtime.Time `gorm:"column:created_at;comment:创建时间" json:"created_at"`
	UpdatedAt  *gtime.Time `gorm:"column:updated_at;comment:更新时间" json:"updated_at"`
	DeletedAt  *gtime.Time `gorm:"column:deleted_at;type:timestamp;comment:删除时间" json:"deleted_at"`
}

// 用户上传文件映射表
func (FileInfo) TableName() string {
	return "file_info"
}

// FileChunk 文件分片信息表
type FileChunk struct {
	gorm.Model

	ChunkId      string      `gorm:"column:chunk_id;size:64;index;unique;comment:分片唯一ID" json:"chunk_id"`
	FileId       string      `gorm:"column:file_id;size:64;index;comment:关联的文件ID" json:"file_id"`
	ChunkIndex   int         `gorm:"column:chunk_index;comment:分片索引号" json:"chunk_index"`
	ChunkSize    int64       `gorm:"column:chunk_size;comment:分片大小(字节)" json:"chunk_size"`
	ChunkPath    string      `gorm:"column:chunk_path;size:512;comment:分片存储路径" json:"chunk_path"`
	ChunkHash    string      `gorm:"column:chunk_hash;size:64;comment:分片哈希值" json:"chunk_hash"`
	IsUploaded   bool        `gorm:"column:is_uploaded;default:false;comment:是否已上传" json:"is_uploaded"`
	UploadUserId string      `gorm:"column:upload_user_id;size:32;comment:上传用户ID" json:"upload_user_id"`
	CreatedAt    *gtime.Time `gorm:"column:created_at;comment:创建时间" json:"created_at"`
	UpdatedAt    *gtime.Time `gorm:"column:updated_at;comment:更新时间" json:"updated_at"`
}

func (FileChunk) TableName() string {
	return "file_chunk"
}

// FtpTransfer FTP文件传输记录表
type FtpTransfer struct {
	gorm.Model

	TransferId   string      `gorm:"column:transfer_id;size:64;index;unique;comment:传输记录ID" json:"transfer_id"`
	FileId       string      `gorm:"column:file_id;size:64;index;comment:关联的文件ID" json:"file_id"`
	TransferType int         `gorm:"column:transfer_type;comment:传输类型(1:上传,2:下载)" json:"transfer_type"`
	FtpServer    string      `gorm:"column:ftp_server;size:255;comment:FTP服务器地址" json:"ftp_server"`
	FtpPort      int         `gorm:"column:ftp_port;comment:FTP服务器端口" json:"ftp_port"`
	FtpPath      string      `gorm:"column:ftp_path;size:512;comment:FTP文件路径" json:"ftp_path"`
	Status       int         `gorm:"column:status;comment:状态(0:进行中,1:成功,2:失败)" json:"status"`
	ErrorMsg     string      `gorm:"column:error_msg;size:512;comment:错误信息" json:"error_msg"`
	UserId       string      `gorm:"column:user_id;size:32;comment:操作用户ID" json:"user_id"`
	StartTime    *gtime.Time `gorm:"column:start_time;comment:开始时间" json:"start_time"`
	EndTime      *gtime.Time `gorm:"column:end_time;comment:结束时间" json:"end_time"`
	CreatedAt    *gtime.Time `gorm:"column:created_at;comment:创建时间" json:"created_at"`
	UpdatedAt    *gtime.Time `gorm:"column:updated_at;comment:更新时间" json:"updated_at"`
}

func (FtpTransfer) TableName() string {
	return "ftp_transfer"
}

// FileAccess 文件访问权限表
type FileAccess struct {
	gorm.Model

	AccessId   string      `gorm:"column:access_id;size:64;index;unique;comment:访问权限ID" json:"access_id"`
	FileId     string      `gorm:"column:file_id;size:64;index;comment:关联的文件ID" json:"file_id"`
	UserId     string      `gorm:"column:user_id;size:32;index;comment:用户ID" json:"user_id"`
	AccessType int         `gorm:"column:access_type;comment:访问类型(1:读,2:写,3:读写)" json:"access_type"`
	ExpireTime *gtime.Time `gorm:"column:expire_time;comment:过期时间,null表示永不过期" json:"expire_time"`
	CreatedAt  *gtime.Time `gorm:"column:created_at;comment:创建时间" json:"created_at"`
	UpdatedAt  *gtime.Time `gorm:"column:updated_at;comment:更新时间" json:"updated_at"`
}

func (FileAccess) TableName() string {
	return "file_access"
}
