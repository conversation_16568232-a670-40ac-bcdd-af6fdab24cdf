/*
******		FileName	:	dao.go
******		Describe	:	此文件主要用于数据库连接管理与表自动创建
******		Date		:	2024-11-12
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:   所有数据库表管理
 */

package dao

import (
	modelFile "ayj_file_back/internal/model/file"
	"database/sql"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	_ "github.com/lib/pq"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var Db *gorm.DB = nil

// 使用 gorm 连接数据
func UseGormOptDb() {
	// 创建数据库（如果不存在）
	createDb()

	// 连接数据库
	connectDatabase()

	// 创建所有模型表
	MigrateAllModels()
}

// 自动创建DB
func createDb() {
	var ctx = gctx.New()
	host, _ := g.Cfg().Get(ctx, "database.host")
	port, _ := g.Cfg().Get(ctx, "database.port")
	dbName, _ := g.Cfg().Get(ctx, "database.name") //	数据库名称

	user, _ := g.Cfg().Get(ctx, "database.user")
	pass, _ := g.Cfg().Get(ctx, "database.pass")

	userDefault, _ := g.Cfg().Get(ctx, "database.default_user")
	passDefault, _ := g.Cfg().Get(ctx, "database.default_pass")

	db, err := sql.Open("postgres", fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=postgres sslmode=disable", host, port, userDefault, passDefault))
	if err != nil {
		g.Log().Error(ctx, err)
	}
	err = db.Ping()
	if err != nil {
		g.Log().Error(ctx, err)
	}
	//	创建数据库
	_, err = db.Exec(fmt.Sprintf("CREATE DATABASE %s", dbName))
	if err != nil {
		g.Log().Info(ctx, err)
	}
	//	创建账号与密码
	_, err = db.Exec(fmt.Sprintf("CREATE USER %s WITH PASSWORD '%s'", user, pass))
	if err != nil {
		g.Log().Info(ctx, err)
	}

	_, err = db.Exec(fmt.Sprintf("GRANT ALL ON DATABASE %s to %s", dbName, user))
	if err != nil {
		g.Log().Error(ctx, err)
	}

	_ = db.Close()
}

// 连接数据库
func connectDatabase() {
	var ctx = gctx.New()
	host, err := g.Cfg().Get(ctx, "database.host")
	port, err := g.Cfg().Get(ctx, "database.port")
	user, err := g.Cfg().Get(ctx, "database.user")
	password, err := g.Cfg().Get(ctx, "database.pass")
	dbName, err := g.Cfg().Get(ctx, "database.name")

	fmt.Println("数据库配置信息", host, port, user, password, dbName, err)
	connectDbInfo := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=disable", host, user, password, dbName, port)

	// 获取数据库连接配置
	var gormConfig gorm.Config

	// 禁用数据库日志输出
	gormConfig.Logger = logger.Default.LogMode(logger.Silent)

	// 连接数据库
	var dbErr error
	Db, dbErr = gorm.Open(postgres.Open(connectDbInfo), &gormConfig)
	if dbErr != nil {
		panic(fmt.Sprintf("数据库连接失败: %v", dbErr))
	}

	fmt.Println("数据库连接成功")
}

// 创建所有模型表
func MigrateAllModels() {
	if Db == nil {
		fmt.Println("数据库连接未初始化，无法创建表")
	} else {
		//	文件管理表
		_ = Db.AutoMigrate(modelFile.FileAccess{})  //	文件访问权限表
		_ = Db.AutoMigrate(modelFile.FileChunk{})   //	文件分片信息表
		_ = Db.AutoMigrate(modelFile.FileInfo{})    //	文件信息表
		_ = Db.AutoMigrate(modelFile.FtpTransfer{}) //	FTP文件传输记录表

		fmt.Println("所有模型表迁移完成")
	}
}
