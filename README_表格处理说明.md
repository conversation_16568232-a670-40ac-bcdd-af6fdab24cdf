# Word表格批量复制工具

## 功能说明
将 `internal/model/user/word.docx` 文件中的表格复制成8份，排列为2列4行的布局，适合A4纸打印和对折裁剪。

## 设计特点
- **性能优先**: 优化的复制算法，快速处理
- **精确布局**: 边距10像素，完美对称设计
- **简洁代码**: 模块化设计，逻辑清晰
- **详细注释**: 每个逻辑块都有序号标注

## 使用方法

### 方法一：自动运行（推荐）
1. 双击运行 `setup_and_run.bat`
2. 脚本会自动：
   - 检测Python环境
   - 安装必要的依赖库
   - 检查输入文件
   - 处理表格并生成新文档

### 方法二：手动运行
```bash
# 1. 安装依赖
pip install python-docx

# 2. 运行脚本
python process_word_table.py
```

## 输入要求
- 输入文件：`internal/model/user/word.docx`
- 文件中必须包含至少一个表格
- 表格格式不限（会保持原有格式）

## 输出结果
- 输出文件：`internal/model/user/word_8_copies_layout.docx`
- 布局：2列 × 4行 = 8个相同表格
- 页面设置：A4纸，边距10像素
- 间距：自动计算，确保完美对称

## 布局设计

```
┌─────────────────────────────────────────┐
│  边距10px                    边距10px   │
│  ┌─────────┐    ┌─────────┐            │
│  │ 表格1   │    │ 表格2   │            │
│  └─────────┘    └─────────┘            │
│                                        │
│  ┌─────────┐    ┌─────────┐            │
│  │ 表格3   │    │ 表格4   │            │
│  └─────────┘    └─────────┘            │
│                                        │
│  ┌─────────┐    ┌─────────┐            │
│  │ 表格5   │    │ 表格6   │            │
│  └─────────┘    └─────────┘            │
│                                        │
│  ┌─────────┐    ┌─────────┐            │
│  │ 表格7   │    │ 表格8   │            │
│  └─────────┘    └─────────┘            │
│  边距10px                    边距10px   │
└─────────────────────────────────────────┘
```

## 打印和裁剪说明
1. **打印设置**：选择A4纸，100%缩放，无边距调整
2. **对折裁剪**：
   - 先沿中间垂直线对折
   - 再沿水平线裁剪
   - 每次裁剪可得到2个表格
   - 总共4次裁剪得到8个相同大小的表格

## 技术特性
- **精确计算**：使用厘米单位确保精度
- **格式保持**：完整复制原表格的字体、对齐等格式
- **批量处理**：一次性生成所有表格
- **错误处理**：完善的异常处理和用户提示

## 系统要求
- Python 3.7 或更高版本
- python-docx 库
- Windows 系统（批处理脚本）

## 故障排除
1. **Python未找到**：请安装Python并添加到系统PATH
2. **依赖库安装失败**：手动运行 `pip install python-docx`
3. **输入文件不存在**：检查文件路径是否正确
4. **权限错误**：确保有文件写入权限

## 接口说明
- **process_word_table()** 主处理函数，负责整体流程控制
- **copy_table_to_cell()** 表格复制函数，处理单个表格的复制和格式化
- **copy_cell_content()** 单元格内容复制函数，保持格式一致性
